import "./sub-project/common-base-module/global";
import "./sub-project/polyfill/abc-polyfill";
import App from "./app";
import { Hippy } from "@hippy/react";
import { init as AbcMobileUiInit } from "@app/abc-mobile-ui";
import { themeInit as AbcMobileThemeInit } from "@app/theme";
import { utilsInit as AbcUtilsInit } from "@app/utils";
import { LogUtils } from "./sub-project/common-base-module/log";
import { sharedPreferences } from "./sub-project/base-business/preferences/shared-preferences";
import { AppInfo } from "./sub-project/base-business/config/app-info";
import { LanguageObserver } from "./sub-project/language/config";
import { getComponentErrorHandler } from "./sub-project/base-ui/base-component";

AbcMobileThemeInit({
    sharedPreferences,
    appInfo: AppInfo,
});

AbcUtilsInit({
    appInfo: AppInfo,
});

AbcMobileUiInit({
    log: LogUtils,
    languageObserver: LanguageObserver,
    getComponentErrorHandler,
});

new Hippy({
    appName: "abcyun",
    entryPage: App,
}).start();
