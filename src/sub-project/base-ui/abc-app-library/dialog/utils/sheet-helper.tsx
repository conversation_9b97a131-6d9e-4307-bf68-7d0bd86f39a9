/**
 * create by <PERSON><PERSON>
 * desc:
 * create date 2021/11/30
 */
import * as React from "react";
import { Style, StyleSheet, Text, View } from "@hippy/react";
import { ABCStyles, Color, Colors, flattenStyles, Sizes, TextStyles } from "../../../../theme";
import { ABCNavigator } from "../../../views/abc-navigator";
import { ToolBar, ToolBarButtonStyle1 } from "../../../tool-bar-button";
import { DeviceUtils } from "../../../utils/device-utils";
import { AbcView } from "../../../views/abc-view";
import _ from "lodash";
import { AssetImageView } from "../../../views/asset-image-view";
import { Spacer } from "../../../index";

interface BottomSheetCloseButtonProps {
    style?: Style | Style[];
    onTap?(): void;
}

export class BottomSheetCloseButton extends React.Component<BottomSheetCloseButtonProps> {
    render(): JSX.Element {
        return (
            <AbcView onClick={this._onTap.bind(this)} style={{ paddingHorizontal: Sizes.dp16, paddingVertical: Sizes.dp16 }}>
                <AssetImageView
                    name={"delete_circle_grey"}
                    style={[{ width: Sizes.dp18, height: Sizes.dp18 }, flattenStyles(this.props.style)]}
                />
            </AbcView>
        );
    }

    private _onTap(): void {
        if (this.props.onTap) this.props.onTap();
    }
}

// @ts-ignore
const styles = StyleSheet.create({
    titleBarContainer: {
        height: Sizes.dp57,
        borderBottomWidth: Sizes.dpHalf,
        justifyContent: "center",
        alignItems: "center",
        borderColor: Colors.dividerLineColor,
        backgroundColor: Colors.white,
        borderRadius: DeviceUtils.isIOS() ? 1 : undefined, // 解决显示BUG
    },
    itemContainer: {
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "center",
    },
    selectIconStyle: { marginRight: Sizes.listHorizontalMargin },
    titleBarTopRadius: { borderTopLeftRadius: Sizes.dp6, borderTopRightRadius: Sizes.dp6 },
});

export class BottomSheetHelper {
    static createTitleBar(
        title: string | JSX.Element,
        showTopRadius?: boolean,
        onClick?: () => void,
        style?: Style | Style[],
        topDistance?: number
    ): JSX.Element {
        let titleView = title;
        if (_.isString(title)) {
            titleView = <Text style={[TextStyles.t18MT1]}>{title}</Text>;
        }
        return (
            <View style={[styles.titleBarContainer, showTopRadius ? styles.titleBarTopRadius : undefined]}>
                {titleView ?? <View />}
                <View style={{ position: "absolute", right: 0, top: topDistance ?? Sizes.dp2 }}>
                    <BottomSheetCloseButton onTap={onClick ? onClick : () => ABCNavigator.pop()} style={style} />
                </View>
            </View>
        );
    }

    static createTitle(title: string, showTitleBottomLine = true, showCloseButton?: boolean): JSX.Element {
        return (
            <View
                style={[
                    styles.titleBarContainer,
                    showTitleBottomLine ? {} : { borderBottomWidth: 0 },
                    {
                        justifyContent: "center",
                    },
                ]}
            >
                <Text style={TextStyles.t18MT1}>{title}</Text>
                {showCloseButton && (
                    <View style={{ position: "absolute", right: 0, top: Sizes.dp2 }}>
                        <BottomSheetCloseButton onTap={() => ABCNavigator.pop()} />
                    </View>
                )}
            </View>
        );
    }

    static createBottomBar(params: { text?: string; hideWhenKeyboardShow?: boolean; onClick?: () => void }): JSX.Element {
        return (
            <ToolBar hideWhenKeyboardShow={params.hideWhenKeyboardShow}>
                <ToolBarButtonStyle1 text={params.text ?? "确定"} onClick={() => params.onClick?.()} />
            </ToolBar>
        );
    }

    static createDefaultHandleBar(params: { onClick?: () => void; color?: Color }): JSX.Element {
        return (
            <View style={[ABCStyles.rowAlignCenter, ABCStyles.bottomLine]}>
                <AbcView style={[Sizes.paddingLTRB(Sizes.dp16, Sizes.dp14)]} onClick={() => ABCNavigator.pop()}>
                    <Text style={[TextStyles.t14MT1.copyWith({ color: Colors.t3, lineHeight: Sizes.dp20 })]}>取消</Text>
                </AbcView>
                <Spacer />
                <AbcView style={[Sizes.paddingLTRB(Sizes.dp16, Sizes.dp14)]} onClick={() => params.onClick?.()}>
                    <Text style={[TextStyles.t14MM.copyWith({ lineHeight: Sizes.dp20, color: params?.color ?? Colors.mainColor })]}>
                        确定
                    </Text>
                </AbcView>
            </View>
        );
    }
}
