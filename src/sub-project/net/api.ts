/**
 * 成都字节星期科技公司
 * <AUTHOR>
 * @date 2020-03-19
 *
 * @description 封装与后台api http请求
 */
import { LogUtils } from "../common-base-module/log";
import _ from "lodash";
import { JsonMapper } from "../common-base-module/json-mapper/json-mapper";
import { errorToStr } from "../common-base-module/utils";
import { AnyType } from "../common-base-module/common-types";
import { environment } from "../base-business/config/environment";
import SseHelper, { SseEventListeners, SseConnectionResult, SimpleSseConfig } from "../base-business/sse";

export interface ABCApiNetworkDelegate {
    extraHeaders(url: string): Promise<Headers | undefined>;

    prefixHost(): string | undefined;

    onRsp(rawRsp: Response, json: any): void;

    /**
     * 请求前回调，如果返回false,请求中断
     * @param method
     * @param path
     */
    onWillRequest(method: string, path: string): Promise<boolean>;
}

class ABCApiError extends Error {
    code: number;
    msg: string;
    detail?: AnyType;
    url?: string;
    method?: string;

    constructor(code: number, msg: string, detail?: AnyType, url?: string, method?: string) {
        super(`code:${code}, msg = ${msg}, url = ${url}, method =${url}`);
        this.code = code;
        this.msg = msg;
        this.detail = detail;
        this.url = url;
        this.method = method;
    }
}

class ABCNetworkError extends Error {
    summaryMessage?: string;
    detailError: any;
    url?: string;
    method?: string;

    constructor(summaryMessage?: string, detailError?: AnyType, url?: string, method?: string) {
        super(`${summaryMessage}:detailError = ${detailError}, url=${url},method=${method}`);
        this.summaryMessage = summaryMessage;
        this.detailError = detailError;
        this.url = url;
        this.method = method;
    }
}

class ABCNetworkTimeoutError extends Error {
    detailError: any;
    url?: string;
    method?: string;

    constructor(detailError?: AnyType, url?: string, method?: string) {
        super(`detailError=${detailError}, url = ${url}, method=${method}`);
        this.detailError = detailError;

        this.url = url;
        this.method = method;
    }
}

interface ReqOptions<T> {
    //请求相关，audioUrl 参数
    queryParameters?: any; //{key:}
    //在post, put,之类请求的body
    body?: any;

    ///处理响应相关
    useBody?: boolean;
    useRsp?: boolean;
    clazz?: { new (): T };

    silent?: boolean; // 是否打印日志， false
    clearUndefined?: boolean; //是否删除queryParameters中undefined数据
}

/**
 * 请求配置构建结果
 */
interface RequestConfig {
    /** 完整的请求 URL */
    fullPath: string;
    /** 查询参数字符串 */
    queryParameters: string;
    /** 请求头 */
    headers: Headers | undefined;
    /** 是否静默模式 */
    silent: boolean;
    /** 请求方法 */
    method: string;
    /** 原始路径 */
    originalPath: string;
}

/**
 * SSE 请求选项接口
 * 继承基础请求选项，添加 SSE 特定配置
 */
interface SseReqOptions<T> extends Omit<ReqOptions<T>, "useBody" | "useRsp" | "clazz"> {
    /** HTTP 方法，默认 GET */
    method?: "GET" | "POST" | "PUT" | "DELETE";
    /** 自定义请求头 */
    headers?: HeadersInit;
    /** 是否自动重连，默认 true */
    autoReconnect?: boolean;
    /** 重连间隔（毫秒），默认 3000 */
    reconnectInterval?: number;
    /** 最大重连次数，默认 5 */
    maxReconnectAttempts?: number;
    /** 连接超时时间（毫秒），默认 30000 */
    connectTimeout?: number;
    /** SSE 事件监听器 */
    listeners?: SseEventListeners;
}

/**
 * SSE 连接错误类
 */
class ABCSseError extends Error {
    code: number;
    msg: string;
    detail?: AnyType;
    url?: string;
    method?: string;
    connectionId?: string;

    constructor(code: number, msg: string, detail?: AnyType, url?: string, method?: string, connectionId?: string) {
        super(`SSE Error - code:${code}, msg = ${msg}, url = ${url}, method = ${method}, connectionId = ${connectionId}`);
        this.code = code;
        this.msg = msg;
        this.detail = detail;
        this.url = url;
        this.method = method;
        this.connectionId = connectionId;
    }
}

class ABCApiNetwork {
    delegate?: ABCApiNetworkDelegate;

    /**
     * Get 请求
     * @param path
     * @param options 参数
     */
    get<T>(path: string, options?: ReqOptions<T>): Promise<T> {
        return this._doRequest("GET", path, options);
    }

    /**
     * post 请求
     * @param path
     * @param options 参数
     */
    post<T>(path: string, options?: ReqOptions<T>): Promise<T> {
        return this._doRequest("POST", path, options);
    }

    /**
     * put 请求
     * @param path
     * @param options 参数
     */
    put<T>(path: string, options?: ReqOptions<T>): Promise<T> {
        return this._doRequest("PUT", path, options);
    }

    /**
     * delete 请求
     * @param path
     * @param options 参数
     */
    delete<T>(path: string, options?: ReqOptions<T>): Promise<T> {
        return this._doRequest("DELETE", path, options);
    }

    /**
     * SSE 连接请求
     * 参考 _doRequest 方法的实现模式，集成 SSE 功能到现有网络架构中
     * @param path 请求路径
     * @param options SSE 请求选项
     */
    async connectSSE<T>(path: string, options?: SseReqOptions<T>): Promise<SseConnectionResult> {
        const method = options?.method || "GET";

        try {
            // 使用抽取的公共方法构建请求配置
            const config = await this._buildRequestConfig(method, path, options);

            // 构建 SSE 特定的请求头
            const sseHeaders = this._buildSseHeaders(config.headers);
            if (!config.silent) {
                console.log(
                    `ABCApiNetwork.connectSSE req fullPath = ${config.fullPath}, method = ${method}, headers = ${JSON.stringify(
                        sseHeaders
                    )}`
                );
                LogUtils.d(`ABCApiNetwork.connectSSE req fullPath = ${config.fullPath}, body = ${JSON.stringify(options?.body)}`);
            }

            // 构建 SSE 配置
            const sseConfig: SimpleSseConfig = {
                url: config.fullPath,
                method: method,
                headers: sseHeaders,
                body: options?.body ? JSON.stringify(options.body) : undefined,
                autoReconnect: options?.autoReconnect !== false,
                reconnectInterval: options?.reconnectInterval || 3000,
                maxReconnectAttempts: options?.maxReconnectAttempts || 5,
            };

            // 创建 SSE 连接
            const connection = await SseHelper.connect(sseConfig, options?.listeners || {});

            if (!config.silent) {
                LogUtils.d(`ABCApiNetwork.connectSSE success connectionId = ${connection.connectionId}, url = ${config.fullPath}`);
            }

            return connection;
        } catch (error) {
            // 尝试获取配置，如果失败则使用默认值
            let config: Partial<RequestConfig>;
            try {
                config = await this._buildRequestConfig(method, path, options);
            } catch (configError) {
                // 如果配置构建失败，使用最小的默认配置
                config = {
                    fullPath: path,
                    silent: options?.silent ?? false,
                    method: method,
                    originalPath: path,
                    queryParameters: "",
                    headers: undefined,
                };
            }

            if (!config.silent) {
                LogUtils.e(`ABCApiNetwork.connectSSE error fullPath = ${config.fullPath}, error = ${errorToStr(error)}`);
            }

            // 处理 SSE 特定错误
            if (error instanceof Error) {
                throw new ABCSseError(500, error.message, error, config.fullPath || path, method);
            }

            throw new ABCSseError(500, "Unknown SSE connection error", error, config.fullPath || path, method);
        }
    }

    /**
     * 构建 SSE 特定的请求头
     * @param baseHeaders 基础请求头
     * @returns 合并后的 SSE 请求头
     */
    private _buildSseHeaders(baseHeaders?: Headers): HeadersInit {
        let finalHeaders = baseHeaders;

        if (!finalHeaders) {
            finalHeaders = new Headers();
        }

        finalHeaders.append("Accept", "text/event-stream");
        finalHeaders.append("Cache-Control", "no-cache");

        return finalHeaders;
    }

    /**
     * 发送网络请求 请求
     * @param method 请求类型
     * @param path 请求路径
     * @param options 参数
     */
    async _doRequest<T>(method: "GET" | "POST" | "PUT" | "DELETE", path: string, options?: ReqOptions<T>): Promise<T> {
        // 使用抽取的公共方法构建请求配置
        const config = await this._buildRequestConfig(method, path, options);

        if (!config.silent) {
            LogUtils.d(
                `ABCApiNetwork._doRequest req fullPath = ${config.fullPath}, method = ${method}, headers = ${JSON.stringify(
                    config.headers
                )}`
            );
            LogUtils.d(`ABCApiNetwork._doRequest req fullPath = ${config.fullPath}, body = ${JSON.stringify(options?.body)}`);
        }

        return fetch(config.fullPath, {
            body: options?.body ? JSON.stringify(options?.body) : undefined,
            method: method,
            headers: config.headers,
        })
            .then(async (rsp) => {
                try {
                    const json = await rsp.json();
                    return {
                        rsp: rsp,
                        json: json,
                        jsonParserError: undefined,
                    };
                } catch (error) {
                    return {
                        rsp: rsp,
                        json: null,
                        jsonParserError: error,
                    };
                }
            })
            .then(({ rsp, json, jsonParserError }) => {
                this.delegate?.onRsp(rsp, json);

                if (!config.silent)
                    LogUtils.d(
                        `ABCApiNetwork._doRequest rsp fullPath = ${config.fullPath}, rsp.ok = ${rsp.ok}, rsp.status = ${
                            rsp.status
                        }, rsp = ${JSON.stringify(json)}`
                    );

                if (!rsp.ok) {
                    const message = json?.error?.message;
                    throw new ABCApiError(rsp.status, message || rsp.statusText, { ...json }, config.fullPath, method);
                }

                if (options && options.useRsp) {
                    return rsp;
                }

                if (jsonParserError) throw jsonParserError;

                if (options && options.useBody) {
                    if (_.isNil(json)) return undefined;

                    if (options.clazz) {
                        return JsonMapper.deserialize(options.clazz, json);
                    }
                    return json;
                }

                const data = json["data"];
                if (_.isNil(data)) return undefined;
                if (options != null && options.clazz) {
                    return JsonMapper.deserialize(options.clazz, data);
                } else return data;
            })
            .catch((e) => {
                if (e instanceof ABCApiError) throw e;
                if (e.code === "-1009" && e.userInfo && e.userInfo.NSLocalizedDescription) {
                    throw new ABCNetworkError(e.userInfo.NSLocalizedDescription as string, e, config.fullPath, method);
                }

                if (e.code === "-1001" && e.userInfo && e.userInfo.NSLocalizedDescription) {
                    throw new ABCNetworkTimeoutError(e, config.fullPath, method);
                }

                if (e === "timeout") throw new ABCNetworkTimeoutError(e, config.fullPath, method);

                if (!config.silent) LogUtils.e(`ABCApiNetwork._doRequest rsp fullPath = ${config.fullPath}, error = ${errorToStr(e)}`);
                throw new ABCNetworkError(undefined, e, config.fullPath, method);
            });
    }

    private _getHeaders(url: string): Promise<Headers | undefined> {
        if (this.delegate) return this.delegate?.extraHeaders(url);

        return Promise.resolve(undefined);
    }

    private get urlPrefix() {
        return this.delegate?.prefixHost() ?? "";
    }

    /**
     * 构建请求配置
     * 抽取 _doRequest 和 connectSSE 的公共逻辑
     * @param method HTTP 方法
     * @param path 请求路径
     * @param options 请求选项
     * @returns 构建好的请求配置
     */
    private async _buildRequestConfig<T extends ReqOptions<any> | SseReqOptions<any>>(
        method: string,
        path: string,
        options?: T
    ): Promise<RequestConfig> {
        const silent = options?.silent ?? false;

        // 调用请求前回调
        const shouldProceed = await this.delegate?.onWillRequest(method, path);
        if (shouldProceed === false) {
            throw new Error("Request cancelled by delegate");
        }

        // 构建查询参数
        const queryParameters = this._buildQueryParameters(options);

        // 构建完整 URL
        const { fullPath, headers } = await this._buildFullPathAndHeaders(path, method, queryParameters);

        return {
            fullPath,
            queryParameters,
            headers,
            silent,
            method,
            originalPath: path,
        };
    }

    /**
     * 构建查询参数字符串
     * @param options 请求选项
     * @returns 查询参数字符串
     */
    private _buildQueryParameters<T extends ReqOptions<any> | SseReqOptions<any>>(options?: T): string {
        let queryParameters = "";

        if (options && options.queryParameters) {
            const keys = Object.getOwnPropertyNames(options.queryParameters);
            keys.forEach((key) => {
                const value = options.queryParameters[key];
                if (options.clearUndefined && _.isNil(value)) {
                    return;
                }
                if (!_.isEmpty(queryParameters)) {
                    queryParameters += "&";
                }
                if (_.isArray(value)) {
                    queryParameters += value
                        .filter((item) => !options.clearUndefined || !_.isNil(item))
                        .map((item) => {
                            return `${key}=${encodeURIComponent(item)}`;
                        })
                        .join("&");
                } else {
                    if (!options.clearUndefined || !_.isNil(value)) {
                        queryParameters += `${key}=${encodeURIComponent(value)}`;
                    }
                }
            });
        }

        if (!_.isEmpty(queryParameters)) {
            queryParameters += "&";
        }

        return queryParameters + "t=" + new Date().getTime();
    }

    /**
     * 构建完整的请求路径
     * @param path 原始路径
     * @param method HTTP 方法
     * @param queryParameters 查询参数
     * @returns 完整的请求路径
     */
    private async _buildFullPathAndHeaders(
        path: string,
        method: string,
        queryParameters: string
    ): Promise<{
        fullPath: string;
        headers: Headers | undefined;
    }> {
        let fullPath: string;

        // 判断是否为完整 URL
        if (path.includes("://")) {
            if (method === "GET") {
                fullPath = `${path}?${queryParameters}`;
            } else {
                fullPath = path;
            }
        } else {
            fullPath = `${this.urlPrefix}/${path}?${queryParameters}`;
        }

        const headers = await this._getHeaders(fullPath);

        // 处理 global 分区的 URL 替换逻辑
        if (environment.isGlobalEnv) {
            fullPath = fullPath.replace("/v2/mobile", "");
        }

        // 特殊配置接口处理
        if (path === "appproperty/propertyList") {
            fullPath = `${environment.serverHostScheme}://${environment._serverNormalHostname}/api/v2/mobile/${path}?${queryParameters}`;
        } else if (path === "ai/analysis/text") {
            fullPath = `${environment.serverHostScheme}://${environment.serverHostName}/api/v2/ai/analysis/text?${queryParameters}`;
        }

        return {
            fullPath,
            headers,
        };
    }
}

export default new ABCApiNetwork();
export { ABCApiError, ABCNetworkError, ABCNetworkTimeoutError, ABCSseError };
export type { SseReqOptions };
