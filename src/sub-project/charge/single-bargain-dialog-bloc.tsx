/**
 * 成都字节星期科技公司
 * <AUTHOR>
 * @date 2020-03-20
 *
 * @description
 */

import { Bloc, BlocEvent } from "../bloc";
import React from "react";
import { actionEvent, EventName } from "../bloc/bloc";
import {
    BargainingBusinessKey,
    ChargeBargainingRule,
    ChargeConfig,
    ChargeForm,
    ChargeFormItem,
    ChargeInvoiceDetailData,
    ChargeInvoiceType,
    ChargeSheetSummary,
    MatchType,
    Promotion,
} from "./data/charge-beans";
import { JsonMapper } from "../common-base-module/json-mapper/json-mapper";
import { Subject } from "rxjs";
import { debounceTime, switchMap } from "rxjs/operators";
import { ChargeAgent, ChargeCalculateRspData } from "./data/charge-agent";
import { ChargeUtils } from "./utils/charge-utils";
import { ABCError } from "../common-base-module/common-error";
import { Toast } from "../base-ui/dialog/toast";
import { errorToStr } from "../common-base-module/utils";
import { ABCNavigator } from "../base-ui/views/abc-navigator";
import { GetClinicBasicSetting, OnlinePropertyConfigProvider } from "../data/online-property-config-provder";
import { OutpatientUtils } from "../outpatient/utils/outpatient-utils";
import { userCenter } from "../user-center";

class State {
    detailData?: ChargeInvoiceDetailData;
    promotions?: Promotion[];
    calculating = false;
    calculateFailed: any; //算费失败错误
    editable = true;
    totalCostPrice?: number; // 成本

    //算费配置
    chargeConfig?: ChargeConfig;

    // 是否超出了可议价的值
    isOverCanAdjustment = false;

    clinicBasicSetting?: GetClinicBasicSetting;
    bargainingRules?: ChargeBargainingRule[];
    // 当前登录者是否有单项收费议价权限
    get hasSingleBargainPermission(): boolean {
        // 首先需要开启了收费单项议价
        if (!this.singleBargainSwitch) return false;
        // 管理员不受控制
        if (userCenter.clinic?.isManager) return true;
        // 有收费权限的可议价
        if (!this.chargeConfig?.assignEmployeeBargain) return true;
        // 指定人员议价
        const rule = this.bargainingRules?.find((rule) => rule.businessKey == BargainingBusinessKey.adjustEmployeeMatch);
        if (!rule) return false;
        return (
            rule.productTypeMatches?.some(
                (match) =>
                    match.type == MatchType.EMPLOYEE && match.typeRefId == (userCenter.employee?.id ?? userCenter.employee?.employeeId)
            ) ?? false
        );
    }
    // 当前登录者是否有整单议价权限
    get hasWholeBargainPermission(): boolean {
        // 首先需要开启了收费单项议价
        if (!this.bargainSwitch) return false;
        // 管理员不受控制
        if (userCenter.clinic?.isManager) return true;
        // 有收费权限的可议价
        if (!this.chargeConfig?.assignEmployeeBargain) return true;
        // 指定人员议价
        const rule = this.bargainingRules?.find((rule) => rule.businessKey == BargainingBusinessKey.adjustEmployeeMatch);
        if (!rule) return false;
        return (
            rule.productTypeMatches?.some(
                (match) =>
                    match.type == MatchType.EMPLOYEE && match.typeRefId == (userCenter.employee?.id ?? userCenter.employee?.employeeId)
            ) ?? false
        );
    }

    get bargainSwitch(): boolean {
        return !!this.chargeConfig?.bargainSwitch;
    }

    get singleBargainSwitch(): boolean {
        return !!this.chargeConfig?.singleBargainSwitch;
    }

    // 当前药品中没有可以议价的项目
    get noCanBargainItem(): boolean {
        // 没有 chargeForms 视为没有可议价项
        if (!this.detailData?.chargeForms?.length) return true;
        // 只要有一个 item 可议价，则返回 false
        return !this.detailData.chargeForms.some((form) => form.chargeFormItems?.some((item) => item.isCanAdjustment));
    }

    clone(): State {
        return Object.assign(new State(), this);
    }
}

class _Event extends BlocEvent {}

class _EventInit extends _Event {
    detailData: ChargeInvoiceDetailData;
    editable: boolean;

    constructor(detailData: ChargeInvoiceDetailData, editable: boolean) {
        super();
        this.detailData = detailData;
        this.editable = editable;
    }
}

class _EventUpdate extends _Event {}

class _EventUpdateCalculatePrice extends _Event {
    calculating: boolean;
    calculateRspData?: ChargeCalculateRspData;
    error?: any;

    constructor(calculating: boolean, calculateRspData?: ChargeCalculateRspData, error?: any) {
        super();
        this.calculating = calculating;
        this.calculateRspData = calculateRspData;
        this.error = error;
    }
}

class _EventChangeItemUnitPrice extends _Event {
    formItem: ChargeFormItem;
    price: number;

    constructor(formItem: ChargeFormItem, price: number) {
        super();
        this.formItem = formItem;
        this.price = price;
    }
}

class _EventChangeItemTotalPrice extends _Event {
    formItem: ChargeFormItem;
    price: number;

    constructor(formItem: ChargeFormItem, price: number) {
        super();

        this.formItem = formItem;
        this.price = price;
    }
}

class _EventSave extends _Event {}

class _EventUpdateTotalPrice extends _Event {
    price: number;

    constructor(price: number) {
        super();
        this.price = price;
    }
}

class _EventUpdateChargeFormPrice extends _Event {
    chargeForm: ChargeForm;
    price?: number;

    constructor(chargeForm: ChargeForm, price?: number) {
        super();
        this.chargeForm = chargeForm;
        this.price = price;
    }
}

class _EventBargainReduction extends _Event {}
class _EventUpdateCanAdjustmentFee extends _Event {
    canAdjustmentFee: boolean;

    constructor(canAdjustmentFee: boolean) {
        super();
        this.canAdjustmentFee = canAdjustmentFee;
    }
}

class SingleBargainDialogBloc extends Bloc<_Event, State> {
    static Context = React.createContext<SingleBargainDialogBloc | undefined>(undefined);
    private _itemUnitPriceChanged = false;
    private _itemTotalPriceChanged = false;
    private _totalPriceChanged = false;

    static fromContext(context: SingleBargainDialogBloc): SingleBargainDialogBloc {
        return context;
    }

    _calculatePriceTrigger = new Subject<number>();

    constructor(detailData: ChargeInvoiceDetailData, editable: boolean) {
        super();

        this.dispatch(new _EventInit(detailData, editable));
    }

    private _innerState!: State;
    get innerState(): State {
        if (!this._innerState) this._innerState = new State();

        return this._innerState;
    }

    initialState(): State {
        return this.innerState;
    }

    createEventHandlers(): Map<EventName, Function> {
        const map = new Map<EventName, Function>();
        map.set(_EventInit, this._mapEventInit);
        map.set(_EventUpdate, this._mapEventUpdate);
        map.set(_EventUpdateCalculatePrice, this._mapEventUpdateCalculatePrice);
        map.set(_EventChangeItemUnitPrice, this._mapEventChangeItemUnitPrice);
        map.set(_EventChangeItemTotalPrice, this._mapEventChangeItemTotalPrice);
        map.set(_EventSave, this._mapEventSave);
        map.set(_EventUpdateTotalPrice, this._mapEventUpdateTotalPrice);
        map.set(_EventUpdateChargeFormPrice, this._mapEventUpdateChargeFormPrice);

        return map;
    }

    /**
     * 争对零售收费的情况，需要遍历所有未收费的项，并把checked设置为true
     */
    private _retailChargeFillChecked(): void {
        const unCharged = OutpatientUtils.canEditWithChargeStatus(this.innerState.detailData?.status);
        if (
            unCharged &&
            (this.innerState.detailData?.type == ChargeInvoiceType.retail ||
                this.innerState.detailData?.type == ChargeInvoiceType.therapy ||
                this.innerState.detailData?.localDraftId)
        ) {
            this.innerState.detailData?.chargeForms?.forEach((item) => {
                item.chargeFormItems?.forEach((subItem) => {
                    if (OutpatientUtils.canEditWithChargeStatus(subItem.status)) {
                        subItem.checked = true;
                    }
                });
            });
        }
    }

    async *_mapEventInit(event: _EventInit): AsyncGenerator<State> {
        this.innerState.detailData = JsonMapper.deserialize(ChargeInvoiceDetailData, event.detailData);
        this._retailChargeFillChecked();
        this.innerState.detailData!.fillKeyIds();
        this.innerState.editable = event.editable;

        this.innerState.chargeConfig = await OnlinePropertyConfigProvider.instance.getChargeConfig().catchIgnore();
        this.innerState.clinicBasicSetting = await OnlinePropertyConfigProvider.instance.getClinicBasicSetting().catchIgnore();
        // 读取收费处可议价人员范围
        this.innerState.bargainingRules = await ChargeAgent.queryChargeBargainingRules({
            businessKeys: [BargainingBusinessKey.adjustEmployeeMatch],
        }).catchIgnore();
        yield this.innerState.clone();

        this.addDisposable(this._calculatePriceTrigger);

        this._calculatePriceTrigger
            .pipe(
                debounceTime(500),
                switchMap(() => {
                    this.dispatch(new _EventUpdateCalculatePrice(true));
                    return this._calculatePrice()
                        .catch((error) => new ABCError(error))
                        .toObservable();
                })
            )
            .subscribe((rsp) => {
                if (rsp instanceof ABCError) {
                    this.dispatch(new _EventUpdateCalculatePrice(false, undefined, rsp.detailError));
                } else {
                    this.innerState.totalCostPrice = rsp?.totalCostPrice;
                    this.dispatch(new _EventUpdateCalculatePrice(false, rsp));
                }
            })
            .addToDisposableBag(this);

        this._calculatePriceTrigger.next(0);
    }

    async *_mapEventUpdate(/*ignored: _EventUpdate*/): AsyncGenerator<State> {
        yield this.innerState;
    }

    async *_mapEventUpdateCalculatePrice(event: _EventUpdateCalculatePrice): AsyncGenerator<State> {
        this._doUpdateCalculatePrice(event);
        yield this.innerState.clone();
    }

    async *_mapEventChangeItemUnitPrice(event: _EventChangeItemUnitPrice): AsyncGenerator<State> {
        event.formItem.expectedTotalPrice = undefined;
        event.formItem.expectedUnitPrice = event.price;
        event.formItem.expectedTotalPriceRatio = undefined;
        this._itemUnitPriceChanged = true;
        if (this.innerState.detailData?.chargeSheetSummary) this.innerState.detailData!.chargeSheetSummary.expectedTotalPrice__ = undefined;
        this._calculatePriceTrigger.next(0);
    }

    async *_mapEventChangeItemTotalPrice(event: _EventChangeItemTotalPrice): AsyncGenerator<State> {
        event.formItem.expectedTotalPrice = event.price;
        event.formItem.expectedUnitPrice = undefined;
        event.formItem.expectedTotalPriceRatio = undefined;
        this._itemTotalPriceChanged = true;
        if (this.innerState.detailData?.chargeSheetSummary) this.innerState.detailData!.chargeSheetSummary.expectedTotalPrice__ = undefined;
        this._calculatePriceTrigger.next(0);
    }

    async *_mapEventSave(/*ignored: _EventSave*/): AsyncGenerator<State> {
        const innerState = this.innerState;
        if (innerState.calculateFailed != null) {
            try {
                this._doUpdateCalculatePrice(new _EventUpdateCalculatePrice(true, undefined, undefined));
                yield innerState.clone();
                const rsp = await this._calculatePrice();
                this._doUpdateCalculatePrice(new _EventUpdateCalculatePrice(false, rsp, undefined));
            } catch (e) {
                this._doUpdateCalculatePrice(new _EventUpdateCalculatePrice(false, undefined, e));
                await Toast.show(`算费失败：${errorToStr(e)}`, { warning: true });
                return;
            } finally {
                yield innerState.clone();
            }
        }
        if (this.innerState.isOverCanAdjustment) {
            return;
        } else {
            ABCNavigator.pop({
                chargeSheet: innerState.detailData,
                itemUnitPriceChanged: this._itemUnitPriceChanged,
                itemTotalPriceChanged: this._itemTotalPriceChanged,
                totalPriceChanged: this._totalPriceChanged,
            });
        }
    }

    async *_mapEventUpdateTotalPrice(event: _EventUpdateTotalPrice): AsyncGenerator<State> {
        const chargeSheetSummary = (this.innerState.detailData!.chargeSheetSummary =
            this.innerState.detailData!.chargeSheetSummary ?? new ChargeSheetSummary());

        //整单议价
        chargeSheetSummary!.expectedTotalPrice__ = event.price;
        this._totalPriceChanged = true;
        this._calculatePriceTrigger.next(0);
    }

    async *_mapEventUpdateChargeFormPrice(event: _EventUpdateChargeFormPrice): AsyncGenerator<State> {
        if (this.innerState.detailData?.chargeSheetSummary) this.innerState.detailData!.chargeSheetSummary.expectedTotalPrice__ = undefined;
        event.chargeForm.expectedTotalPrice = event.price;
        event.chargeForm.expectedPriceFlag = undefined;
        this._calculatePriceTrigger.next(0);
    }

    @actionEvent(_EventBargainReduction)
    async *_mapEventBargainReduction(): AsyncGenerator<State> {
        const detailData = this.innerState.detailData;
        const needPayFee = detailData?.chargeSheetSummary?.expectedTotalPrice__ ?? detailData?.chargeSheetSummary?.needPayFee ?? 0;
        const draftAdjustmentFee = detailData?.chargeSheetSummary?.draftAdjustmentFee ?? 0;
        detailData!.chargeSheetSummary!.expectedTotalPrice__ = needPayFee - draftAdjustmentFee;
        detailData!.chargeSheetSummary!.draftAdjustmentFee = undefined;
        this.update();
    }

    @actionEvent(_EventUpdateCanAdjustmentFee)
    async *_mapEventUpdateCanAdjustmentFee(event: _EventUpdateCanAdjustmentFee): AsyncGenerator<State> {
        this.innerState.isOverCanAdjustment = event.canAdjustmentFee;
        if (this.innerState.isOverCanAdjustment) {
            await Toast.show("议价金额不能超过最大可议价值");
            return;
        }
    }

    _doUpdateCalculatePrice(event: _EventUpdateCalculatePrice): void {
        this.innerState.calculating = event.calculating;
        this.innerState.calculateFailed = event.error;
        if (!event.calculateRspData) return;

        ChargeUtils.syncChargeInvoiceDetail(this.innerState.detailData, event.calculateRspData);
    }

    async _calculatePrice(): Promise<ChargeCalculateRspData | undefined> {
        return ChargeUtils.calculatingPrice(this.innerState.detailData!);
    }

    public update(): void {
        this.dispatch(new _EventUpdate());
    }

    requestChangeItemUnitPrice(formItem: ChargeFormItem, price: number): void {
        this.dispatch(new _EventChangeItemUnitPrice(formItem, price));
    }

    public requestChangeItemTotalPrice(formItem: ChargeFormItem, price: number): void {
        this.dispatch(new _EventChangeItemTotalPrice(formItem, price));
    }

    public requestSave(): void {
        this.dispatch(new _EventSave());
    }

    public requestUpdateTotalPrice(price: number): void {
        this.dispatch(new _EventUpdateTotalPrice(price));
    }

    public requestUpdateChargeFormPrice(chargeForm: ChargeForm, price?: number): void {
        this.dispatch(new _EventUpdateChargeFormPrice(chargeForm, price));
    }

    requestBargainReduction(): void {
        this.dispatch(new _EventBargainReduction());
    }
    requestUpdateCanAdjustmentFee(canAdjustment: boolean): void {
        this.dispatch(new _EventUpdateCanAdjustmentFee(canAdjustment));
    }
}

export { SingleBargainDialogBloc, State };
