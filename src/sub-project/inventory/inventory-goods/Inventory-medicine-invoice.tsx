/**
 * create by <PERSON><PERSON>
 * desc: 药品物资详情页
 * create date 2021/1/28
 */
import React from "react";
import { Text, View } from "@hippy/react";
import { InventoryMedicineInvoiceBloc } from "./Inventory-medicine-invoice-bloc";
import { BaseBlocNetworkPage } from "../../base-ui/base-page";
import { ABCStyles, Colors, Sizes, TextStyles } from "../../theme";
import { SizedBox, Spacer, ToolBar } from "../../base-ui";
import { BlocHelper } from "../../bloc/bloc-helper";
import { ListSettingItem } from "../../base-ui/views/list-setting-item";
import { AbcListView } from "../../base-ui/list/abc-list-view";
import { GoodsBatchInfoDetail } from "../data/inventory-bean";
import { BaseComponent } from "../../base-ui/base-component";
import { NumberUtils, TimeUtils } from "../../common-base-module/utils";
import { ABCUtils } from "../../base-ui/utils/utils";
import { AbcView } from "../../base-ui/views/abc-view";
import { ignore } from "../../common-base-module/global";
import { AssetImageView } from "../../base-ui/views/asset-image-view";
import _ from "lodash";
import { GoodsType, SubClinicPricePriceMode } from "../../base-business/data/beans";
import { userCenter } from "../../user-center";
import { AbcTag } from "../../base-ui/abc-app-library/common/abc-tag";
import { ABCNavigator } from "../../base-ui/views/abc-navigator";
import { Clinic, ModuleIds } from "../../user-center/user-center";

interface InventoryMedicineInvoiceProps {
    goodsId: string;
    clinicId: string;
    isShowPosition?: boolean;
}

enum _BottomId {
    check = 1,
    purchase,
    out,
    changePrice,
}

export class InventoryMedicineInvoice extends BaseBlocNetworkPage<InventoryMedicineInvoiceProps, InventoryMedicineInvoiceBloc> {
    private _bottomGroup = [
        new _BottomItem(_BottomId.check, "inventory_storage_check", "盘点", false, () => {
            this.bloc.requestTemporaryCheck();
        }),
        new _BottomItem(_BottomId.purchase, "inventory_purchase", "采购", true),
        new _BottomItem(_BottomId.out, "inventory_storage_out", "出库", false, () => {
            this.bloc.requestInventoryOut();
        }),
        new _BottomItem(_BottomId.changePrice, "change_price", "改价", false, () => {
            this.bloc.requestModifyGoodsPrice();
        }),
    ];

    constructor(props: InventoryMedicineInvoiceProps) {
        super(props);
        this.bloc = new InventoryMedicineInvoiceBloc({
            goodsId: props.goodsId,
            clinicId: props.clinicId,
            isShowPosition: props?.isShowPosition,
        });
    }

    static async show(options: InventoryMedicineInvoiceProps): Promise<string> {
        if (!!userCenter.clinic?.isDrugstoreButler) {
            return DrugstoreInventoryMedicineInvoice.show(options);
        } else {
            return ABCNavigator.navigateToPage(<InventoryMedicineInvoice {...options} />);
        }
    }

    getAppBarTitle(): string {
        return "药品/物资详情";
    }

    getAPPBarCustomTitle(): JSX.Element | undefined {
        const state = this.bloc.currentState,
            isOpenMultiplePharmacy = state.pharmacyInfoConfig?.isOpenMultiplePharmacy,
            pharmacyName = state.currentPharmacy?.name;
        return isOpenMultiplePharmacy ? (
            <View style={[ABCStyles.rowAlignCenter]}>
                <Text style={TextStyles.t16MB}>{"药品/物资详情"}</Text>
                <Text style={TextStyles.t14NM}>{`${isOpenMultiplePharmacy ? "(" + pharmacyName + ")" : ""}`}</Text>
            </View>
        ) : undefined;
    }

    componentDidMount(): void {
        super.componentDidMount();
        BlocHelper.connectLoadingStatus(this.bloc, this);
    }

    private _renderBottomItem(item: _BottomItem): JSX.Element {
        const color = item.disable || !this.bloc.currentState.canEdit ? Colors.T2 : Colors.T1;
        return (
            <View
                key={item.type}
                style={[ABCStyles.centerChild, { backgroundColor: Colors.white }]}
                onClick={() => (item.disable || !this.bloc.currentState.canEdit ? ignore() : item.onClick?.())}
            >
                <AssetImageView
                    style={{
                        width: Sizes.dp24,
                        height: Sizes.dp24,
                        //@ts-ignore
                        opacity: item.disable || !this.bloc.currentState.canEdit ? 0.5 : 1,
                    }}
                    name={item.iconName}
                    ignoreTheme={true}
                />
                <SizedBox height={Sizes.dp6} />
                <Text style={TextStyles.t14NT1.copyWith({ color: color })}>{item.text}</Text>
            </View>
        );
    }

    // 是否拥有盘点、报损权限
    hasEditCheckPermission(moduleId: number): boolean {
        if (!userCenter.clinic) return false;
        const { roleId, moduleIds } = userCenter.clinic;
        // 管理员直接拥有权限
        if (roleId === Clinic.ROLEID_MANAGER) {
            return true;
        }
        // 如果没有模块ID，则没有权限
        if (!moduleIds) return false;
        const ids = moduleIds.split(",").map((item) => parseInt(item, 10)); // 明确基数为10
        // 检查是否包含特定的模块ID
        return ids.includes(ModuleIds.MODULE_ID_NONE) || ids.includes(ModuleIds.MODULE_ID_INVENTORY) || ids.includes(moduleId);
    }

    protected _renderBottomGroup(): JSX.Element {
        const state = this.bloc.currentState;
        if (!!state.currentPharmacy) {
            // 是否拥有盘点权限
            const isEditCheck = this.hasEditCheckPermission(ModuleIds.MODULE_ID_INVENTORY_STOCK);
            // 是否拥有出库权限
            const isEditOut = this.hasEditCheckPermission(ModuleIds.MODULE_ID_INVENTORY_LOSS_OUT);
            this._bottomGroup?.forEach((item) => {
                if (item.type == _BottomId.check) {
                    item.disable = !isEditCheck;
                }
                if (item.type == _BottomId.out) {
                    item.disable = !isEditOut;
                }
            });
        }
        if (!!state.currentPharmacy?.type) {
            this._bottomGroup.map((item) => {
                if (item.type != _BottomId.changePrice) {
                    item.disable = true;
                }
                return item;
            });
        }
        return <ToolBar>{this._bottomGroup.map((item) => this._renderBottomItem(item))}</ToolBar>;
    }

    private _renderStatisticInfo(options: {
        title: string;
        content?: string | number;
        errorInfo?: string;
        showError?: boolean;
        bottomLine?: boolean;
    }): JSX.Element {
        const { title, content, errorInfo, showError, bottomLine = true } = options;
        return (
            <ListSettingItem
                title={title}
                bottomLine={bottomLine}
                minTitleWidth={Sizes.dp100}
                contentBuilder={() => {
                    return (
                        <View style={[ABCStyles.rowAlignCenter, { flex: 1 }]}>
                            <Text style={TextStyles.t16NT1}>{`${content}`}</Text>
                            {showError && <Text style={[TextStyles.t12NR2, { marginLeft: Sizes.dp6 }]}>{`${errorInfo}`}</Text>}
                        </View>
                    );
                }}
            />
        );
    }

    private _renderEditFormView(): JSX.Element {
        const state = this.bloc.currentState,
            goodsInfo = state.goodsDetail,
            inventoryStock = state.goodsInventoryStock ?? goodsInfo,
            currentPharmacy = state.currentPharmacy,
            purchaseInfo = state.purchaseInfo,
            checkDrugMaterialCost = state.checkDrugMaterialCost,
            checkDrugMaterialProfit = state.checkDrugMaterialProfit;
        const { negativeProfitWarnFlag, shortageWarnFlag, turnoverDaysWarnFlag } = purchaseInfo;
        const purchaseMarkup = goodsInfo?.priceType == SubClinicPricePriceMode.purchaseMarkup;
        let packagePrice = 0;
        // const chainPackagePrice = goodsInfo?.chainPackagePrice ?? 0;
        let piecePrice = 0;
        if (state.isChain) {
            packagePrice = goodsInfo?.chainPackagePrice ?? 0;
            piecePrice = goodsInfo?.chainPiecePrice ?? 0;
        } else {
            packagePrice = goodsInfo?.packagePrice ?? 0;
            piecePrice = goodsInfo?.piecePrice ?? 0;
        }

        const showPrice = !(goodsInfo?.type == GoodsType.material && !goodsInfo?.isMedicalMaterial);
        return (
            <View style={[{ backgroundColor: Colors.white, paddingLeft: Sizes.dp16 }]}>
                {showPrice && (
                    <View>
                        {checkDrugMaterialCost && (
                            <ListSettingItem
                                title={"最近进价"}
                                minTitleWidth={Sizes.dp100}
                                bottomLine={true}
                                content={
                                    goodsInfo?.lastPackageCostPrice == undefined
                                        ? "-"
                                        : ABCUtils.formatMoney(goodsInfo?.lastPackageCostPrice, !goodsInfo?.isChineseMedicine)
                                }
                            />
                        )}

                        {!userCenter.clinic?.isNormalClinic && (
                            <ListSettingItem
                                title={"总部定价"}
                                minTitleWidth={Sizes.dp100}
                                bottomLine={true}
                                content={`${state.goodsInventoryStock?.__chainPackagePrice ?? ""} ${
                                    goodsInfo?.packageUnit?.length ? "/" + goodsInfo.packageUnit : ""
                                }`}
                            />
                        )}
                        {!userCenter.clinic?.isChainAdminClinic && (
                            <ListSettingItem
                                title={"门店售价"}
                                bottomLine={true}
                                minTitleWidth={Sizes.dp100}
                                content={`${
                                    purchaseMarkup
                                        ? goodsInfo?.retailPricePureRange
                                        : ABCUtils.formatMoney(packagePrice, !goodsInfo?.isChineseMedicine)
                                } ${goodsInfo?.packageUnit?.length ? "/" + goodsInfo.packageUnit : ""}`}
                            />
                        )}

                        {!goodsInfo?.isChineseMedicine && !!goodsInfo?.dismounting && (
                            <ListSettingItem
                                title={"拆零价"}
                                bottomLine={true}
                                minTitleWidth={Sizes.dp100}
                                content={`${ABCUtils.formatMoney(piecePrice, !goodsInfo?.isChineseMedicine)} ${
                                    goodsInfo?.pieceUnit?.length ? "/" + goodsInfo.pieceUnit : ""
                                }`}
                            />
                        )}
                        {checkDrugMaterialProfit &&
                            this._renderStatisticInfo({
                                title: "毛利率",
                                content: NumberUtils.percent(purchaseInfo?.profRat),
                                errorInfo: "毛利率为负，建议调整价格",
                                showError: negativeProfitWarnFlag && state.canEdit,
                            })}
                    </View>
                )}
                {this._renderStatisticInfo({
                    title: "当前库存",
                    bottomLine: true,
                    content: !!inventoryStock?.dispGoodsCount
                        ? inventoryStock?.dispGoodsCount
                        : `0${goodsInfo?.packageUnit ?? goodsInfo?.pieceUnit ?? ""}`,
                    errorInfo: `低于预警值，请及时补货`,
                    showError: shortageWarnFlag && state.canEdit,
                })}
                {this._renderStatisticInfo({
                    title: "可售库存",
                    bottomLine: true,
                    content: inventoryStock?.dispStockGoodsCount ?? 0,
                    errorInfo: `低于预警值，请及时补货`,
                    showError: shortageWarnFlag && state.canEdit,
                })}
                {checkDrugMaterialCost &&
                    !currentPharmacy?.isVirtual &&
                    this._renderStatisticInfo({
                        title: "药品成本",
                        content: _.isNumber(purchaseInfo?.totalCost) ? ABCUtils.formatMoney(purchaseInfo.totalCost, true) : "-",
                    })}
                {this._renderStatisticInfo({
                    title: userCenter.clinic?.isDrugstoreButler ? "30日均销量" : "日均销量",
                    content: _.isUndefined(purchaseInfo.recentAvgSell)
                        ? "-"
                        : `${NumberUtils.count1(purchaseInfo.recentAvgSell)}${
                              goodsInfo?.isChineseMedicine ? goodsInfo.pieceUnit : goodsInfo?.packageUnit
                          }`,
                })}
                {!currentPharmacy?.isVirtual &&
                    this._renderStatisticInfo({
                        title: "周转天数",
                        content: _.isNumber(purchaseInfo?.turnoverDays) ? `${purchaseInfo?.turnoverDays}天` : "-",
                        errorInfo: `少于${purchaseInfo.warnConfigTurnOverDays}天，请及时补货`,
                        showError: turnoverDaysWarnFlag && state.canEdit,
                    })}
                {userCenter.clinic?.isChainAdminClinic &&
                    currentPharmacy?.isNormalPharmacy &&
                    this.props?.isShowPosition &&
                    this._renderStatisticInfo({
                        title: "柜号",
                        content: goodsInfo?.position ?? "-",
                    })}
            </View>
        );
    }

    private _renderMedicineCardView(): JSX.Element {
        const state = this.bloc.currentState,
            goodsInfo = state.goodsDetail;
        return (
            <AbcView
                style={[ABCStyles.bottomLine, Sizes.paddingLTRB(Sizes.dp16, Sizes.dp10), { backgroundColor: Colors.white }]}
                onClick={() => {
                    this.bloc.requestModifyGoodsBaseInfo();
                }}
            >
                <View style={[ABCStyles.rowAlignCenter]}>
                    <Text style={[TextStyles.t16MT1.copyWith({ lineHeight: Sizes.dp24 }), { flexShrink: 1 }]} numberOfLines={1}>
                        {goodsInfo?.displayName ?? ""}
                    </Text>
                    <SizedBox width={Sizes.dp8} />
                    <Text style={[TextStyles.t14NT4.copyWith({ lineHeight: Sizes.dp20 })]}>
                        {(goodsInfo?.isChineseMedicine ? goodsInfo?.extendSpec : goodsInfo?.packageSpec) ?? ""}
                    </Text>
                    <Spacer />
                </View>
                <SizedBox height={Sizes.dp4} />
                <View style={[ABCStyles.rowAlignCenter]}>
                    <Text style={[TextStyles.t14NT4.copyWith({ lineHeight: Sizes.dp20 })]}>{goodsInfo?.displayTypeName ?? ""}</Text>
                    <SizedBox width={Sizes.dp8} />
                    <Text style={[TextStyles.t14NT4.copyWith({ lineHeight: Sizes.dp20 }), { flexShrink: 1 }]} numberOfLines={1}>
                        {goodsInfo?.displayManufacturer ?? ""}
                    </Text>
                    <Spacer />
                    {!state.canEdit && <Text style={TextStyles.t14NR2.copyWith({ lineHeight: Sizes.dp20 })}>已停用</Text>}
                </View>
            </AbcView>
        );
    }

    private _renderMedicineContent(): JSX.Element {
        const views: JSX.Element[] = [];
        views.push(this._renderEditFormView());
        views.push(<SizedBox height={Sizes.dp8} />);
        const state = this.bloc.currentState,
            batchInfo = state.goodsBatchDetail,
            goodsBatchDetail = state.goodsBatchDetail,
            goodsBatchList = state.goodsBatchList,
            checkDrugMaterialCost = state.checkDrugMaterialCost;

        if (!!goodsBatchDetail) {
            views.push(
                <View
                    style={[
                        ABCStyles.rowAlignCenter,
                        Sizes.paddingLTRB(Sizes.listHorizontalMargin, Sizes.dp4),
                        ABCStyles.bottomLine,
                        { backgroundColor: Colors.white },
                    ]}
                >
                    <Text style={TextStyles.t12NT4.copyWith({ lineHeight: Sizes.dp20 })}>批次信息</Text>
                    <Text style={TextStyles.t12NT4.copyWith({ lineHeight: Sizes.dp20 })}>{`（${batchInfo?.count ?? 0}）`}</Text>
                </View>
            );
            goodsBatchList.forEach((item) => {
                views.push(<_GoodsBatchDetailView batch={item} checkDrugMaterialCost={checkDrugMaterialCost} />);
            });
        }
        return (
            <AbcListView
                style={{ marginTop: Sizes.dp8, flex: 1 }}
                initialListSize={20}
                finished={!state.hasMoreBatch}
                finishedText={""}
                scrollEventThrottle={300}
                numberOfRows={views.length}
                dataSource={views}
                renderRow={(item) => item}
                getRowKey={(index) => index.toString()}
                onEndReached={() => {
                    this.bloc.requestLoadMoreBatch();
                }}
            />
        );
    }

    renderContent(): JSX.Element {
        return (
            <View style={{ flex: 1 }}>
                {this._renderMedicineCardView()}
                {this._renderBottomGroup()}
                {this._renderMedicineContent()}
            </View>
        );
    }
}
export class DrugstoreInventoryMedicineInvoice extends InventoryMedicineInvoice {
    getAppBarTitle(): string {
        return "商品详情";
    }
    getAPPBarCustomTitle(): JSX.Element | undefined {
        return undefined;
    }
    static async show(options: InventoryMedicineInvoiceProps): Promise<string> {
        return ABCNavigator.navigateToPage(<DrugstoreInventoryMedicineInvoice {...options} />);
    }

    _renderBottomGroup(): JSX.Element {
        return <View />;
    }
}
interface _GoodsBatchDetailViewProps {
    batch: GoodsBatchInfoDetail;
    checkDrugMaterialCost: boolean; // 能否查看药品物资成本
}

class _GoodsBatchDetailView extends BaseComponent<_GoodsBatchDetailViewProps> {
    constructor(props: _GoodsBatchDetailViewProps) {
        super(props);
    }

    render(): JSX.Element {
        const batch = this.props.batch;
        const checkDrugMaterialCost = this.props.checkDrugMaterialCost;
        return (
            <View
                style={[
                    ABCStyles.bottomLine,
                    {
                        backgroundColor: Colors.white,
                        paddingHorizontal: Sizes.dp16,
                        paddingVertical: Sizes.dp8,
                    },
                ]}
            >
                <View style={{ flexDirection: "row" }}>
                    <Text style={[TextStyles.t16NT1, { lineHeight: Sizes.dp24 }]}>{`${batch.batchId}`}</Text>
                    <Text
                        style={[TextStyles.t16NT1, { lineHeight: Sizes.dp24, marginHorizontal: Sizes.dp8, flexShrink: 1 }]}
                        numberOfLines={1}
                    >
                        {`${batch.supplierName ?? "盘点入库"}`}
                    </Text>
                    {(batch.expireStatus ?? 0) >= 10 && (
                        <AbcTag
                            text={batch.statusName ?? ""}
                            borderColor={Colors.t3}
                            textStyle={TextStyles.t10NT2.copyWith({ lineHeight: Sizes.dp10 })}
                            style={{ borderWidth: Sizes.dp1, ...Sizes.paddingLTRB(Sizes.dp4, Sizes.dp4, Sizes.dp4, Sizes.dp4) }}
                        />
                    )}
                    <Spacer />
                    {checkDrugMaterialCost && (
                        <Text style={[TextStyles.t16NT1, { lineHeight: Sizes.dp24 }]}>{`${batch.purchasePrice} (进)`}</Text>
                    )}
                </View>
                <SizedBox height={Sizes.dp8} />
                <View style={ABCStyles.rowAlignCenter}>
                    <Text style={[TextStyles.t14NT2, { lineHeight: Sizes.dp20 }]}>
                        {`${TimeUtils.formatDate(batch.inDate, "yyyy/MM/dd")}入库`}
                    </Text>
                    <Text
                        style={[
                            TextStyles.t14NT2.copyWith({
                                lineHeight: Sizes.dp20,
                                color:
                                    batch.expireStatus == 1 && batch.packageCount && batch.pieceCount
                                        ? Colors.Y2
                                        : batch.expiredWarnFlag
                                        ? Colors.Y2
                                        : Colors.T2,
                            }),
                            { marginHorizontal: Sizes.dp8 },
                        ]}
                        numberOfLines={1}
                    >
                        {`${
                            TimeUtils.formatDate(batch.displayExpiryDate, "yyyy/MM/dd").length
                                ? TimeUtils.formatDate(batch.displayExpiryDate, "yyyy/MM/dd")
                                : ""
                        }${batch.expireStatus == 1 ? "【过期】" : ""}`}
                    </Text>
                    <Text style={[TextStyles.t14NT2, { lineHeight: Sizes.dp20, flexShrink: 1 }]} numberOfLines={1}>
                        {`余${batch.stockDisplay}`}
                    </Text>
                </View>
            </View>
        );
    }
}

class _BottomItem {
    type: number;
    iconName: string;
    text: string;
    disable: boolean;

    onClick?(): void;

    constructor(type: number, iconName = "scan", text: string, disable = false, onClick?: () => void) {
        this.type = type;
        this.iconName = iconName;
        this.text = text;
        this.disable = disable;
        this.onClick = onClick;
    }
}
