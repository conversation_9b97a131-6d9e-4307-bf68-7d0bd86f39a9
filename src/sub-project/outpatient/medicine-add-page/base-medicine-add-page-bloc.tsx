/**
 * create by den<PERSON><PERSON><PERSON>
 * @Description
 * <AUTHOR>
 * @CreateDate 2024/4/29
 * @Copyright 成都字节流科技有限公司© 2024
 */

import React from "react";
import { Bloc, BlocEvent } from "../../bloc";
import { actionEvent } from "../../bloc/bloc";
import { sharedPreferences } from "../../base-business/preferences/shared-preferences";
import {
    ClinicAgent,
    ClinicAirPharmacyConfig,
    ClinicVirtualPharmacyConfig,
    EmployeesMeConfig,
} from "../../base-business/data/clinic-agent";
import {
    ClinicDispensingConfig,
    OnlinePropertyConfigProvider,
    OutpatientDataPermissionGoodsPriceType,
} from "../../data/online-property-config-provder";
import { OutpatientAgent } from "../data/outpatient";
import { MedicineAddType, OutpatientConst, PsychotropicNarcoticTypeList, conversionTypeList } from "../data/outpatient-const";
import { userCenter } from "../../user-center";
import { InventoryClinicConfig, PharmacyListConfig } from "../../inventory/data/inventory-bean";
import { WesternMedicineConfig, WesternMedicineConfigProvider } from "../data/western-medicine-config";
import {
    ChineseMedicineConfig,
    ChineseMedicineConfigProvider,
    ChineseMedicineUsageInfo,
    ChineseUsageItemInfo,
    PrescriptionDefaultUsage,
} from "../data/chinese-medicine-config";
import {
    BasePrescriptionForm,
    ChinesePrescriptionProcessingInfoRsp,
    MedicineBatchInfoList,
    OutpatientInvoiceDetail,
    PrescriptionChineseForm,
    PrescriptionFormDelivery,
    PrescriptionFormItem,
    PrescriptionInfusionForm,
    PrescriptionWesternForm,
} from "../data/outpatient-beans";
import { of, Subject } from "rxjs";
import { debounceTime, switchMap } from "rxjs/operators";
import { ChargeAgent } from "../../charge/data/charge-agent";
import {
    AntibioticEnum,
    ChineseGoodType,
    ChineseMedicineSpecType,
    DeliveryInfo,
    GoodsInfo,
    GoodsSubType,
    GoodsType,
    GoodsTypeId,
    Patient,
    PsychotropicNarcoticEmployee,
    PsychotropicNarcoticTypeEnum,
    UsageInfo,
} from "../../base-business/data/beans";
import {
    AirPharmacyCalculateForm,
    AirPharmacyCalculateReq,
    AirPharmacyOrderItem,
    AirPharmacyVendor,
    MedicineScopeId,
    PharmacyType,
    UsageScopeId,
} from "../../charge/data/charge-bean-air-pharmacy";
import { JsonMapper } from "../../common-base-module/json-mapper/json-mapper";
import { OutpatientUtils } from "../utils/outpatient-utils";
import { MedicineUsage } from "./medicine-add-page-bean";
import { errorToStr, UUIDGen } from "../../common-base-module/utils";
import { ABCError } from "../../common-base-module/common-error";
import _, { cloneDeep, isNil, isUndefined } from "lodash";
import { ChargeUtils } from "../../charge/utils/charge-utils";
import { fromPromise } from "rxjs/internal-compatibility";
import { AbcTextInput } from "../../base-ui/views/abc-text-input";
import { Toast } from "../../base-ui/dialog/toast";
import { AbcDialog } from "../../base-ui/abc-app-library";
import { MedicineAddPageUtils } from "./medicine-add-page-utils";
import { pxToDp } from "../../base-ui/utils/ui-utils";
import { delayed } from "../../common-base-module/rxjs-ext/rxjs-ext";
import { OutpatientPrescriptionTemplateSearchDialog } from "../prescription-template/outpatient-prescription-template-search-page";
import { MedicineTemplateAddType, MedicineTemplateAddTypeDialog } from "./views/medicine-template-add-type-dialog";
import { PrescriptionTemplateCategory, PrescriptionTemplateInfo } from "../data/prescription-template-bean";
import { AirPharmacyKeLiConfig, ChinesePrescriptionUsage, InfusionPrescriptionUsage } from "../data/medicine-add-bean";
import { DialogIndex, showConfirmDialog, showQueryDialog } from "../../base-ui/dialog/dialog-builder";
import { Text, View } from "@hippy/react";
import { Colors, TextStyles } from "../../theme";
import { GoodsAgent } from "../../data/goods/goods-agent";
import { ABCUtils } from "../../base-ui/utils/utils";
import {
    ChargeForm,
    ChargeSourceFormType,
    ChargeStatus,
    ChineseAirPharmacyBagsParam,
    DispensingFormItemsSourceItemType,
} from "../../charge/data/charge-beans";
import { WesternMedicine } from "../../../assets/medicine_usage/western-medicine-config";
import { Completer } from "../../common-base-module/async/completer";
import { CDSSAgent } from "../data/cdss";
import { SaveTemplateDialog } from "../views/save-template-dialog";
import { ABCNavigator } from "../../base-ui/views/abc-navigator";
import { clinicSharedPreferences } from "../../base-business/preferences/scoped-shared-preferences";
import { SharedReferenceConstants } from "../data/shared-reference-constants";
import { AirPharmacyDetail } from "../air-pharmacy/select-air-pharmacy-dialog-bloc";
import { CHINESE_PRESCRIPTION_USAGE_DEFAULT } from "../outpatient-invoice-page-bloc";
import ChineseMedicine from "../../../assets/medicine_usage/chinese-medicine-config";
import { MedicineChinesePanelType } from "../views/medicine-prescription-edit-page/medicine-chinese-prescription-edit-page";
import { showBottomPanel } from "../../base-ui/abc-app-library/panel";
import { ChineseAirPharmacyBagsDialog } from "./views/chinese-air-pharmacy-bags-dialog";
import { DeliveryInfoEditPage } from "../../charge/delivery-info-edit-page";
import { AirPharmacyDeliveryInfoEditPage } from "../../charge/air-pharmacy-delivery-info-edit-page";
import { MedicineUsagePicker } from "../../base-ui/picker/medicine-usage-picker";
import { SelectAirPharmacy, SelectAirPharmacyDialog } from "../air-pharmacy/select-air-pharmacy-dialog";
import { ChinesePrescriptionProcessView } from "./views/modify-usage-view/chinese-prescription-process-view";
import { MedicineBatchInfoDialog } from "./views/medicine-batch-info-dialog";
import { PrescriptionTypeDialog } from "./views/prescription-type-dialog";
import { JingMaPrescriptionRequiredPage } from "../views/jing-ma-prescription-required-page/jing-ma-prescription-required-page";
import { showOptionsBottomSheet } from "../../base-ui/dialog/bottom_sheet";

const CHINESE_ARRANGEMENT = "CHINESE_ARRANGEMENT";

class _Event extends BlocEvent {}

class _EventInit extends _Event {}

class _EventUpdate extends _Event {
    state?: State;

    constructor(state?: State) {
        super();
        this.state = state;
    }
}

class _EventCacheSearchInput extends _Event {
    searchInput: AbcTextInput | null;

    constructor(searchInput: AbcTextInput | null) {
        super();
        this.searchInput = searchInput;
    }
}

class _EventRecordPartLayout extends _Event {
    key: string;
    layoutY: number;

    constructor(key: string, layoutY: number) {
        super();
        this.key = key;
        this.layoutY = layoutY;
    }
}

class _EventAddMedicinePrescription extends _Event {
    goodsInfo: GoodsInfo;
    group?: number;

    constructor(goodsInfo: GoodsInfo, group?: number) {
        super();
        this.goodsInfo = goodsInfo;
        this.group = group;
    }
}

class _EventSubmit extends _Event {}

class _EventCopyTemplate extends _Event {
    type?: MedicineAddType;
    constructor(type?: MedicineAddType) {
        super();
        this.type = type;
    }
}

class BaseUpdateMedicineEvent extends _Event {
    formItem: PrescriptionFormItem;

    constructor(formItem: PrescriptionFormItem) {
        super();
        this.formItem = formItem;
    }
}

class BaseEditMedicineUsageEvent extends BaseUpdateMedicineEvent {
    value?: string;
    constructor(formItem: PrescriptionFormItem, value?: string) {
        super(formItem);
        this.value = value;
    }
}

class _EventUpdateWesternMedicineGroupIndex extends BaseUpdateMedicineEvent {}

class _EventResetWesternAstt extends BaseUpdateMedicineEvent {}
class _EventDeleteWesternRemark extends BaseUpdateMedicineEvent {}
class _EventDeleteMedicine extends BaseUpdateMedicineEvent {}
class _EventUpdateWesternMedicineAst extends BaseUpdateMedicineEvent {
    ast: number;
    constructor(formItem: PrescriptionFormItem, ast: number) {
        super(formItem);
        this.ast = ast;
    }
}

class _EventModifyMedicineSelfProvidedStatus extends BaseUpdateMedicineEvent {
    status: number;
    constructor(formItem: PrescriptionFormItem, status: number) {
        super(formItem);
        this.status = status;
    }
}

class _EventUpdateWesternRemark extends BaseEditMedicineUsageEvent {}

class _EventUpdateInfusionPrescriptionIvgtt extends _Event {
    groupId: number;
    ivgtt: number;
    constructor(groupId: number, ivgtt: number) {
        super();
        this.groupId = groupId;
        this.ivgtt = ivgtt;
    }
}

class _EventDeleteGroup extends _Event {
    groupId: number;
    constructor(groupId: number) {
        super();
        this.groupId = groupId;
    }
}

class _EventSearchGoods extends _Event {
    keyword: string;

    constructor(props: string) {
        super();
        this.keyword = props;
    }
}

class _EventChineseSwitchType extends _Event {}

class _EventModifyMedicinePharmacy extends BaseUpdateMedicineEvent {
    pharmacy: PharmacyListConfig;
    isModifySelf: boolean;
    constructor(formItem: PrescriptionFormItem, pharmacy: PharmacyListConfig, isModifySelf?: boolean) {
        super(formItem);
        this.pharmacy = pharmacy;
        this.isModifySelf = isModifySelf ?? false;
    }
}

class _EventUpdateWesternMedicineUsage extends BaseEditMedicineUsageEvent {}
class _EventUpdateWesternMedicineFreq extends BaseEditMedicineUsageEvent {}
class _EventUpdateWesternDosageCount extends BaseEditMedicineUsageEvent {}
class _EventUpdateDosageUnit extends BaseEditMedicineUsageEvent {}
class _EventUpdateWesternMedicineDays extends BaseEditMedicineUsageEvent {}
class _EventUpdateWesternAmountUnitCount extends BaseEditMedicineUsageEvent {}
class _EventUpdateWesternAmountUnit extends BaseEditMedicineUsageEvent {}
class _EventUpdateChineseMedicineAmount extends BaseEditMedicineUsageEvent {}
class _EventSelectChineseMedicineBoilMethod extends BaseEditMedicineUsageEvent {}
class EventUpdateChinesePrescription extends _Event {
    value: string;
    constructor(value: string) {
        super();
        this.value = value;
    }
}
class _EventUpdateChinesePrescriptionDosageAmount extends EventUpdateChinesePrescription {}
class _EventSelectChinesePrescriptionUsage extends EventUpdateChinesePrescription {}
class _EventSelectChinesePrescriptionDailyDosage extends EventUpdateChinesePrescription {}
class _EventSelectChinesePrescriptionFreq extends EventUpdateChinesePrescription {}
class _EventSelectChinesePrescriptionUsageUsageDays extends EventUpdateChinesePrescription {}
class _EventSelectChinesePrescriptionUsageLevel extends EventUpdateChinesePrescription {}
class _EventUpdateChinesePrescriptionRequirement extends _Event {}
class _EventSelectChineseGranuleOptions extends _Event {}
class _EventInitEqConversionRule extends _Event {
    value: number;
    constructor(value: number) {
        super();
        this.value = value;
    }
}

class _EventContinueSearch extends _Event {
    focus: boolean;

    constructor(focus: boolean) {
        super();
        this.focus = focus;
    }
}

class _EventChineseMedicineSpecType extends _Event {
    type: number;
    forceTrans?: boolean;
    pharmacyType?: number;

    constructor(type: number, forceTrans?: boolean, pharmacyType?: number) {
        super();
        this.type = type;
        this.forceTrans = forceTrans;
        this.pharmacyType = pharmacyType;
    }
}

class _EventBackPage extends _Event {}

class _EventDeleteWesternMedicineGroupIndex extends _Event {}

class _EventModifyEditGroupState extends _Event {
    status: boolean;
    constructor(status: boolean) {
        super();
        this.status = status;
    }
}
class _EventModifyPrescriptionType extends _Event {}
class _EventModifyPrescriptionNarcoticType extends _Event {
    type?: number;
    constructor(type?: number) {
        super();
        this.type = type;
    }
}
class _EventSaveTemplate extends _Event {}
class _EventChangeArrangement extends _Event {}
class _EventAddMedicineGroup extends _Event {}
class _EventModifyPharmacyType extends _Event {}
class _EventCheckGoodsBatchInfo extends _Event {}

class _EventUpdateCalculatePrice extends _Event {
    calculating: boolean;
    error: any;
    calculateRspData?: OutpatientInvoiceDetail | null;
    shouldNotCopyExpectedPrice?: boolean;

    constructor(options: {
        calculateRspData?: OutpatientInvoiceDetail | null;
        calculating: boolean;
        error: any;
        shouldNotCopyExpectedPrice?: boolean;
    }) {
        super();
        this.calculating = options.calculating;
        this.calculateRspData = options.calculateRspData;
        this.error = options.error;
        this.shouldNotCopyExpectedPrice = options.shouldNotCopyExpectedPrice ?? false;
    }
}

class _EventAdjustDiscount extends BaseUpdateMedicineEvent {
    discount: number;
    type: string;
    constructor(formItem: PrescriptionFormItem, discount: number, type: string) {
        super(formItem);
        this.discount = discount;
        this.type = type;
    }
}
class _EventAdjustTotalPrice extends BaseUpdateMedicineEvent {
    totalPrice: number;
    type: string;
    constructor(formItem: PrescriptionFormItem, totalPrice: number, type: string) {
        super(formItem);
        this.totalPrice = totalPrice;
        this.type = type;
    }
}

class _EventAddChineseMedicineUsage extends _Event {
    tabType?: MedicineChinesePanelType;

    constructor(tabType?: MedicineChinesePanelType) {
        super();
        this.tabType = tabType;
    }
}
class _EventUpdateMedicineStateScopedId extends _Event {}
class _EventSelectAirExpress extends _Event {
    pharmacyType: PharmacyType;
    constructor(pharmacyType: PharmacyType) {
        super();
        this.pharmacyType = pharmacyType;
    }
}

export class State<T extends BasePrescriptionForm = BasePrescriptionForm> {
    //搜索相关字段
    keyword?: string;
    loading = false;
    loadError?: any;
    searchInfo?: Array<GoodsInfo>;
    searchListNoStock?: Array<GoodsInfo>;
    //换算当量
    eqConversionRule = userCenter.enableEqCoefficient ? ChineseMedicineSpecType.chinesePiece : null; // 0:按等效饮片量开方 1: 按实际颗粒量开方
    calculating?: boolean;
    calculateFailed?: any;
    //错误定位滚动相关
    layoutState: Map<string, number> = new Map<string, number>();
    //在添加重复药品时，需要重新拉起对应药品的总量键盘，设置一个Completer,让对应的项进行消费
    pendingActiveFocusItem?: Completer<void>;
    currentFocusItem?: string | null; //当前聚焦操作的项目(异常滚动)
    currentFocusGroupId?: number | null;

    formDetail!: T;
    originFormDetail!: T;
    outpatientSheetDetail?: OutpatientInvoiceDetail;
    medicineType!: MedicineAddType;
    airPharmacyTotalPrice = 0; //空中药房-代煎、颗粒算费价格
    //多药房当前选中药房信息(只针对本地药房，不包含空中药房、代煎代配)
    get selectPharmacyInfo(): PharmacyListConfig | undefined {
        const formDetail = this.formDetail as unknown as PrescriptionChineseForm;
        return (
            formDetail?.pharmacyInfo ??
            ({
                no: formDetail?.pharmacyNo,
                name: formDetail?.pharmacyName,
                type: formDetail?.pharmacyType,
            } as PharmacyListConfig)
        );
    }
    set selectPharmacyInfo(info: PharmacyListConfig | undefined) {
        Object.assign(this.formDetail, {
            pharmacyNo: info?.no,
            pharmacyName: info?.name,
            pharmacyType: info?.type,
            pharmacyInfo: info,
        });
    }
    currentAirPharmacyKeLiConfig?: AirPharmacyKeLiConfig;
    isEditGroup = false; //是否处于删除状态
    showErrorHint = false;
    infusionFormUsageGroup: Map<number, InfusionPrescriptionUsage> = new Map<number, InfusionPrescriptionUsage>();
    /**
     * 门诊处方配置项目
     * @description 用于配置成药处方的皮试显示
     */
    showAstCheck?: boolean;

    //配置相关
    arrangement = true; // 中医处方-排列熟悉
    chineseMedicineConfig!: ChineseMedicineConfig;
    westernMedicineConfig?: WesternMedicineConfig;
    employeesMeConfig?: EmployeesMeConfig;
    dispensingConfig?: ClinicDispensingConfig;
    //goods配置相关信息--此处关注多药房
    pharmacyInfoConfig?: InventoryClinicConfig;
    airPharmacyConfig?: ClinicAirPharmacyConfig;
    virtualPharmacyConfig?: ClinicVirtualPharmacyConfig;
    chinesePrescriptionSupportMix?: number; //中药处方是否允许混开饮片颗粒
    searchChineseSpecType?: number; // 中药处方-混开时 搜索的specType
    ableChangePharmacyType = true;
    isOpenVirtualPharmacy?: boolean; //是否开通虚拟药房（代煎代配）
    isEditLocalPharmacy?: boolean; //是否可选择本地药房
    allowAntibioticList?: AntibioticEnum[]; //允许的抗菌药物
    //开药时是否校验药物类型
    get allowAntibiotic(): boolean {
        return this.allowAntibioticList != undefined;
    }

    //当前是否开启代加工
    get isOpenProcess(): boolean {
        return !!this.dispensingConfig?.isDecoction;
    }
    get ableUseAirPharmacy(): boolean {
        return (
            (!!this.virtualPharmacyConfig?.isVirtualPharmacyOpen ||
                (this.airPharmacyConfig?.isSupportAirPharmacy && this.airPharmacyConfig.openSwitch == 1)) ??
            false
        );
    }

    /**
     * 中药处方代加工信息
     */
    get process(): ChinesePrescriptionProcessingInfoRsp {
        const formDetail = this.formDetail as unknown as PrescriptionChineseForm;
        return formDetail.processInfo;
    }

    //能否开出库存为0的药配置(空中药房、虚拟药房不受库存不足的开关控制)
    get canCheckWithoutStock(): boolean {
        return !(userCenter.inventoryClinicConfig?.stockGoodsConfig?.disableNoStockGoods == 1) || !!this.formDetail?.pharmacyType;
    }

    //不允许混开中药处方饮片颗粒
    get unableChinesePrescriptionSupportMix(): boolean {
        return this.chinesePrescriptionSupportMix == 0;
    }

    get showGoodPrice(): boolean {
        return this.employeesMeConfig?.employeeDataPermission?.outpatient?.goodsPrice == OutpatientDataPermissionGoodsPriceType.allowedAll;
    }
    get showTotalPrice(): boolean {
        return this.employeesMeConfig?.employeeDataPermission?.outpatient?.goodsPrice != OutpatientDataPermissionGoodsPriceType.notAllowed;
    }
    get totalCount(): number {
        return this.formDetail.prescriptionFormItems?.length ?? 0;
    }
    get totalPrice(): number {
        const singleFormPrice =
            this.formDetail.prescriptionFormItems?.reduce((pre, formItem) => {
                const { unitCount, unit, fractionPrice } = formItem;
                if (unitCount == null || unit == null) {
                    return pre;
                }
                let formItemPrice = 0
                // 之前的算费规则存在bug，会导致多算金额，后台算法如下：
                // 算费规则 unitCount * unitPrice * 总剂数 ，最后➕fractionPrice
                if (formItem.specialRequirement !== "【自备】") {
                    const unitPrice = formItem.goodsInfo.unitPriceWithUnit(unit);
                    formItemPrice += unitCount * unitPrice ;
                    if (this.isChinesePrescription) {
                        formItemPrice *= (this.formDetail.doseCount ?? 1);
                    }
                    return pre + formItemPrice + (fractionPrice ?? 0)
                }
                return formItemPrice;
            }, 0) ?? 0;

        return singleFormPrice;
    }
    //获取议价后的总价
    get getBargainTotalPrice(): number {
        return (
            this.formDetail.prescriptionFormItems?.reduce((pre, formItem) => {
                const { unitCount, unit, fractionPrice, expectedTotalPrice, totalPrice } = formItem;
                if (unitCount == null || unit == null) {
                    return pre;
                }
                if (formItem.specialRequirement !== "【自备】") {
                    const unitPrice = formItem.goodsInfo.unitPriceWithUnit(unit);
                    return pre + (expectedTotalPrice ?? totalPrice ?? unitCount * unitPrice + (fractionPrice ?? 0));
                }
                return pre;
            }, 0) ?? 0
        );
    }

    get isChinesePrescription(): boolean {
        return this.medicineType == MedicineAddType.chinese;
    }
    get isInfusionPrescription(): boolean {
        return this.medicineType == MedicineAddType.infusion;
    }
    get isWesternPrescription(): boolean {
        return this.medicineType == MedicineAddType.western;
    }
    get clinicSharedPreferencesKey(): string {
        let clinicSharedPreferencesKey: string = SharedReferenceConstants.LAST_SELECT_VENDOR_INFO;
        if (this.isWesternPrescription) {
            clinicSharedPreferencesKey = SharedReferenceConstants.LAST_SELECT_PHARMACY_INFO_WESTERN;
        } else if (this.isInfusionPrescription) {
            clinicSharedPreferencesKey = SharedReferenceConstants.LAST_SELECT_PHARMACY_INFO_INFUSION;
        }
        return clinicSharedPreferencesKey;
    }
    get isExamination(): boolean {
        return this.medicineType == MedicineAddType.examination;
    }
    get chineseMedicineSpecType(): number {
        return ChineseMedicineSpecType.typeFromName(this.formDetail?.specification)
            ? ChineseMedicineSpecType.chineseGranule
            : ChineseMedicineSpecType.chinesePiece;
    }

    get pharmacy(): number {
        return this.formDetail.pharmacyType ?? PharmacyType.normal;
    }

    get hasChanged(): boolean {
        return !_.isEqual(this.formDetail, this.originFormDetail);
    }
    /**
     * 开通药诊互通功能
     */
    get isOpenCoClinic(): boolean {
        return !!this.pharmacyInfoConfig?.isOpenCoClinicPharmacy && this.outpatientSheetDetail?.isOnline != 1;
    }

    getPrescriptionItemListWithGroupId(groupId = ChargeUtils.maxGroupId): PrescriptionFormItem[] {
        return this.formDetail.prescriptionFormItems?.filter((formItem) => (formItem.groupId || ChargeUtils.maxGroupId) == groupId) ?? [];
    }

    getPrescriptionItemListChunkGroupId(): Map<number, PrescriptionFormItem[]> {
        const prescriptionFormItems = this.formDetail.prescriptionFormItems ?? [];
        const chunkList: Map<number, PrescriptionFormItem[]> = new Map();
        prescriptionFormItems.forEach((item) => {
            const groupId = item.groupId ?? ChargeUtils.maxGroupId;
            const chunkItemList = chunkList.get(groupId) ?? [];
            chunkItemList.push(item);
            chunkList.set(groupId, chunkItemList);
        });
        const sortedMap = new Map(Array.from(chunkList).sort((a, b) => a[0] - b[0]));
        return sortedMap;
    }

    computedPrescriptionItemListSort(): void {
        const sortedMap = this.getPrescriptionItemListChunkGroupId();
        const _newList: PrescriptionFormItem[] = [];
        sortedMap.forEach((prescriptionItemList) => {
            _newList.push(...prescriptionItemList);
        });
        this.formDetail.prescriptionFormItems = _newList;
    }

    clone(): State {
        return Object.assign(new State(), this);
    }
    copyTo(state: State): void {
        Object.assign(state, this);
    }
}

class ScrollToFocusItemState extends State {
    static fromState(state: State): ScrollToFocusItemState {
        const newState = new ScrollToFocusItemState();
        state.copyTo(newState);
        return newState;
    }
}

export class BaseMedicineAddPageBloc<T extends BasePrescriptionForm = BasePrescriptionForm> extends Bloc<_Event, State> {
    static Context = React.createContext<BaseMedicineAddPageBloc | undefined>(undefined);
    private readonly isHideProcessInfo: boolean;
    private _loadVendorList = new Subject();
    private _loadVirtualVendorList = new Subject();
    private _calculatePriceTrigger = new Subject();
    private _airPharmacyCalculateTrigger = new Subject(); //  空中药房算费（饮片-代煎或者颗粒，需要调用，其他情况不需要）
    private _searchGoodsInfoTrigger = new Subject<number>(); // 搜索相关trigger
    private _searchInput?: AbcTextInput | null;
    private _searchClinicId?: string;

    constructor(props: {
        medicineType: MedicineAddType;
        formDetail?: T;
        outpatientDetail?: OutpatientInvoiceDetail;
        showAstCheck?: boolean;
        isHideProcessInfo?: boolean;
        ableChangePharmacyType?: boolean;
        isOpenVirtualPharmacy?: boolean;
        isEditLocalPharmacy?: boolean;
        allowAntibiotic?: AntibioticEnum[];
    }) {
        super();
        const {
            medicineType,
            formDetail,
            outpatientDetail,
            showAstCheck,
            isHideProcessInfo,
            ableChangePharmacyType,
            isOpenVirtualPharmacy,
            isEditLocalPharmacy,
            allowAntibiotic,
        } = props;
        this.isHideProcessInfo = isHideProcessInfo ?? false;

        this.innerState.medicineType = medicineType;
        !!formDetail && (this.innerState.formDetail = formDetail);
        this.innerState.outpatientSheetDetail = cloneDeep(outpatientDetail);
        this.innerState.showAstCheck = showAstCheck;
        this.innerState.ableChangePharmacyType = ableChangePharmacyType ?? true;
        this.innerState.isOpenVirtualPharmacy = isOpenVirtualPharmacy;
        this.innerState.isEditLocalPharmacy = isEditLocalPharmacy ?? true;
        this.innerState.allowAntibioticList = allowAntibiotic;

        this.dispatch(new _EventInit()).then();
    }

    private _innerState!: State;

    get innerState(): State {
        if (!this._innerState) this._innerState = new State();

        return this._innerState;
    }

    static fromContext(context: BaseMedicineAddPageBloc): BaseMedicineAddPageBloc {
        return context;
    }

    initialState(): State {
        return this.innerState;
    }

    public update(state?: State): void {
        this.dispatch(new _EventUpdate(state)).then();
    }

    _initHistoryAirPharmacy(): void {
        const _pharmacyInfo = clinicSharedPreferences.getObject(this.innerState.clinicSharedPreferencesKey);
        if (!_pharmacyInfo) return;
        const formDetail = this.innerState.formDetail as PrescriptionChineseForm;
        if (formDetail.vendor) {
            if (
                formDetail.vendor?.vendorId == _pharmacyInfo.vendor?.vendorId &&
                (!!_pharmacyInfo.vendor?.vendorUsageScopeId
                    ? formDetail.vendor?.vendorUsageScopeId == _pharmacyInfo.vendor?.vendorUsageScopeId
                    : true)
            ) {
                formDetail.vendor = Object.assign(formDetail.vendor, _pharmacyInfo.vendor);
            }
            return;
        }
        if (_pharmacyInfo) {
            if (_pharmacyInfo.pharmacyType == PharmacyType.normal || _pharmacyInfo.pharmacyType == PharmacyType.coClinic) {
                if (!formDetail.prescriptionFormItems?.length) {
                    this.innerState.selectPharmacyInfo = _pharmacyInfo.multiplePharmacyInfo;
                }
            } else {
                Object.assign(formDetail, {
                    doseCount: _pharmacyInfo.doseCount,
                    pharmacyType: _pharmacyInfo.pharmacyType,
                    pharmacyNo: _pharmacyInfo?.pharmacyNo,
                    usageScopeId: _pharmacyInfo?.usageScopeId,
                    medicineStateScopeId: !!_pharmacyInfo.medicineStateScopeId
                        ? _pharmacyInfo.medicineStateScopeId
                        : _pharmacyInfo.vendor?.vendorAvailableMedicalStates?.[0]?.medicalStatId,
                    vendorId: _pharmacyInfo.vendor?.vendorId,
                    vendorName: _pharmacyInfo.vendor?.vendorName,
                    vendorUsageScopeId: _pharmacyInfo.vendor?.vendorUsageScopeId,
                });
            }
            if (this.innerState.isChinesePrescription && !formDetail.prescriptionFormItems?.length) {
                formDetail.vendor = _pharmacyInfo.pharmacyType == PharmacyType.normal ? undefined : _pharmacyInfo.vendor;
                const _specificationType = _pharmacyInfo.usageScopeId == "7" ? "中药颗粒" : "中药饮片";
                if (formDetail.specification != _specificationType) {
                    formDetail.specification = _specificationType;
                }
                // 解决已有处方单进入后，袋数丢失问题
                formDetail.processBagUnitCount = formDetail.processBagUnitCount ?? _pharmacyInfo?.processBagUnitCount;
            }
            this.update();
        }
        if (this.innerState.isChinesePrescription) {
            this.innerState.searchChineseSpecType = this.innerState.chineseMedicineSpecType;
        }
    }

    //清空议价信息
    _clearAdjustmentFee(formDetail: PrescriptionFormItem, isChangePharmacy?: boolean): void {
        if (!formDetail) return;
        const { isWesternPrescription, isInfusionPrescription, isChinesePrescription } = this.innerState;
        if (isWesternPrescription) {
            this.innerState.outpatientSheetDetail?.prescriptionWesternForms?.forEach((form) => {
                const sameItem = form.prescriptionFormItems?.find((t) => t.keyId == formDetail.keyId);
                if (sameItem) {
                    sameItem.expectedTotalPriceRatio = undefined;
                    sameItem.expectedTotalPrice = undefined;
                    sameItem.expectedUnitPrice = undefined;
                    sameItem.isTotalPriceChanged = 0;
                    sameItem.isUnitPriceChanged = 0;
                    if (!!isChangePharmacy) sameItem.currentUnitPrice = undefined;
                    sameItem.unitAdjustmentFeeLastModifiedBy = userCenter.employee?.id ?? userCenter.employee?.employeeId;
                }
            });
        } else if (isInfusionPrescription) {
            this.innerState.outpatientSheetDetail?.prescriptionInfusionForms?.forEach((form) => {
                const sameItem = form.prescriptionFormItems?.find((t) => t.keyId == formDetail.keyId);
                if (sameItem) {
                    sameItem.expectedTotalPriceRatio = undefined;
                    sameItem.expectedTotalPrice = undefined;
                    sameItem.expectedUnitPrice = undefined;
                    sameItem.isTotalPriceChanged = 0;
                    sameItem.isUnitPriceChanged = 0;
                    if (!!isChangePharmacy) sameItem.currentUnitPrice = undefined;
                    sameItem.unitAdjustmentFeeLastModifiedBy = userCenter.employee?.id ?? userCenter.employee?.employeeId;
                }
            });
        } else if (isChinesePrescription) {
            const form = this.innerState.formDetail as PrescriptionChineseForm;
            const pharmacyType = form?.vendor?.pharmacyType ?? form.pharmacyType;
            form.expectedTotalPrice = undefined;
            const sameItem = form.prescriptionFormItems?.find((t) => {
                if (!pharmacyType) return t.keyId == formDetail.keyId;
                return t?.displayName == formDetail?.displayName;
            });
            if (sameItem) {
                sameItem.expectedTotalPriceRatio = undefined;
                sameItem.expectedTotalPrice = undefined;
                sameItem.expectedUnitPrice = undefined;
                sameItem.isTotalPriceChanged = 0;
                sameItem.isUnitPriceChanged = 0;
                if (!!isChangePharmacy) sameItem.currentUnitPrice = undefined;
                sameItem.unitAdjustmentFeeLastModifiedBy = userCenter.employee?.id ?? userCenter.employee?.employeeId;
            }
        }
        this._calculatePriceTrigger.next();
    }
    _clearAdjustmentFeeFormDetail(formDetail: BasePrescriptionForm): void {
        formDetail.prescriptionFormItems?.forEach((formItem) => {
            formItem.expectedTotalPriceRatio = undefined;
            formItem.expectedTotalPrice = undefined;
            formItem.expectedUnitPrice = undefined;
            formItem.isTotalPriceChanged = 0;
            formItem.isUnitPriceChanged = 0;
            formItem.unitAdjustmentFeeLastModifiedBy = userCenter.employee?.id ?? userCenter.employee?.employeeId;
        });
    }

    /**
     * 计算中药饮片加工袋数
     * 用法用量：x日y剂，1日z次
     * 每剂煎药袋数 =(z * x) / y  若计算结果为小数，保留小数，但总袋数向上取整
     * @params  canCallAirCalculate ---是否调用空中药房算费,只针对颗粒来说，因为颗粒调用的次数要少点（代煎-药品克数、剂数、每日剂量、频率、服用量,加工袋数,总袋数均会调用算费；颗粒--药品克数、剂数,加工）
     */
    async computedTisanesSack(canCallAirCalculate?: boolean): Promise<void> {
        const formDetail = this.innerState.formDetail as PrescriptionChineseForm,
            { chineseMedicineSpecType } = this.innerState,
            { usageScopeId, medicineStateScopeId, freq, usageLevel, dailyDosage, doseCount } = formDetail;
        const pharmacyType = formDetail.vendor?.pharmacyType ?? formDetail.pharmacyType;
        let usageLevelList: ChineseUsageItemInfo[] = [];
        const z =
            !!freq && ChineseMedicine.freq.map((t) => t.name).includes(freq) && freq.indexOf("1日") > -1 ? Number(freq.substr(2, 1)) : 0;
        const selDailyDosage = ChineseMedicine.dailyDosage.find((t) => t.name == dailyDosage);
        const x = !!selDailyDosage ? selDailyDosage?.daysCount : 0;
        const y = !!selDailyDosage ? selDailyDosage?.dosageCount : 0;
        //本地药房饮片--加工通过process
        //空中药房饮片（代煎）--加工通过chinesePrescriptionUsage用法

        // 空中药房不同的 制法会对应不同的服用量
        const UsageInfoMap = ChineseMedicineUsageInfo.GetChineseUsageParam;
        const value = UsageInfoMap(medicineStateScopeId) || UsageInfoMap(usageScopeId);
        let w = 0;
        if (value && value.usageLevel) {
            usageLevelList = value.usageLevel;
            w = usageLevelList?.find((t) => t.name == usageLevel)?.value ?? 0;
        }
        const keliSingleBags = ((z * x) / y) * w;

        const result = await ChargeAgent.processBagCountCalculate({
            dailyDosage: dailyDosage,
            doseCount: doseCount,
            freq: freq,
            pharmacyType: pharmacyType,
            type: chineseMedicineSpecType == ChineseMedicineSpecType.chinesePiece ? 1 : 2,
            usageLevel: usageLevel,
        }).catchIgnore();
        if (!result) return;
        const { bagUnitCount, bagTotalCount } = result;
        if (!pharmacyType) {
            formDetail.processBagUnitCount = bagUnitCount;
            formDetail.totalProcessCount = bagTotalCount;
        } else {
            if (medicineStateScopeId == MedicineScopeId.daiJian) {
                formDetail.processBagUnitCount = bagUnitCount;
                formDetail.totalProcessCount = bagTotalCount;
            }
            if (medicineStateScopeId == MedicineScopeId.keLi) {
                //袋数只能为1，2，3，4，6；如果计算为5，向下取整成4，超出6，则填6
                formDetail.processBagUnitCount = !!y
                    ? Math.ceil(keliSingleBags) > 4 && Math.ceil(keliSingleBags) < 6
                        ? 4
                        : Math.ceil(keliSingleBags) > 6
                        ? 6
                        : Math.ceil(keliSingleBags)
                    : undefined;
            }
            if (
                pharmacyType == PharmacyType.air &&
                ((canCallAirCalculate && medicineStateScopeId == MedicineScopeId.keLi) || medicineStateScopeId == MedicineScopeId.daiJian)
            ) {
                this._airPharmacyCalculateTrigger.next();
            }
        }
        this.update();
    }

    /**
     * 特殊的用法，需要增加服用天数字段，隐藏剂量字段
     */
    isSpecialUsages(): boolean {
        const _arr = ["制膏", "制丸", "打粉"];
        return _arr.indexOf(this.innerState.formDetail.usage ?? "") > -1 ?? false;
    }

    /**
     * 空中药房--当前用法用量是否与配置匹配
     * @param field
     * @param fieldName
     */
    setAirPharmacyUsage(field: string, fieldName?: string): boolean {
        const formDetail = this.innerState.formDetail as PrescriptionChineseForm;
        const vendorInfo = formDetail.vendor,
            businessScopeConfig = vendorInfo?.businessScopeConfig;
        if (!vendorInfo || !businessScopeConfig) return false;
        switch (field) {
            case "usage":
                return !!businessScopeConfig?.usageOptions?.find((t) => t.name == fieldName);
            case "usageLevel":
                return !!businessScopeConfig?.usageLevelOptions?.find((t) => t.name == fieldName);
            case "dosage":
                return !!businessScopeConfig?.dosageOptions?.find((t) => t.name == fieldName);
            case "freq":
                return !!businessScopeConfig?.freqOptions?.find((t) => t.name == fieldName);
            default:
                return false;
        }
    }

    /**
     * @desc 同步详情中的供应商和药房
     * @desc 针对中药处方
     *  @ignore
     */
    syncFormDetailVendorAndPharmacy(): void {
        const { isChinesePrescription } = this.innerState;
        if (!isChinesePrescription) return;
        const formDetail = this.innerState.formDetail as PrescriptionChineseForm;
        formDetail.vendor = formDetail.vendor;
        formDetail.pharmacyInfo = formDetail.pharmacyInfo;
    }

    @actionEvent(_EventUpdateCalculatePrice)
    async *_mapEventUpdateCalculatePrice(event: _EventUpdateCalculatePrice): AsyncGenerator<State> {
        await this._doUpdateCalculatePrice(event);
        const { isWesternPrescription, isInfusionPrescription, isChinesePrescription } = this.innerState;
        const formDetail = this.innerState.formDetail;
        const westernForm = this.innerState.outpatientSheetDetail?.prescriptionWesternForms;
        const infusionForm = this.innerState.outpatientSheetDetail?.prescriptionInfusionForms;
        const chineseForm = this.innerState.outpatientSheetDetail?.prescriptionChineseForms;

        let currentForm: any[] | undefined = [];
        if (isWesternPrescription) {
            currentForm = westernForm;
        } else if (isInfusionPrescription) {
            currentForm = infusionForm;
        } else if (isChinesePrescription) {
            currentForm = chineseForm;
        }

        currentForm?.forEach((form: any) => {
            formDetail.prescriptionFormItems?.forEach((medicine) => {
                const formItem = form?.prescriptionFormItems?.find((item: PrescriptionFormItem) => item.keyId == medicine.keyId);
                if (formItem) {
                    Object.assign(medicine, {
                        expectedTotalPrice: formItem.expectedTotalPrice,
                        expectedUnitPrice: formItem.expectedUnitPrice,
                        expectedTotalPriceRatio: formItem.expectedTotalPriceRatio,
                        totalPrice: formItem.totalPrice,
                        totalPriceRatio: formItem.totalPriceRatio,
                        unitPrice: formItem.unitPrice,
                        fractionPrice: formItem.fractionPrice,
                    });
                }
            });
        });
        this.update();
    }

    @actionEvent(_EventCacheSearchInput)
    async *_mapEventCacheSearchInput(event: _EventCacheSearchInput): AsyncGenerator<State> {
        this._searchInput = event.searchInput;
    }

    @actionEvent(_EventAddMedicinePrescription)
    async *_mapEventAddMedicinePrescription(event: _EventAddMedicinePrescription): AsyncGenerator<State> {
        const goodsInfo = event.goodsInfo;
        if (!goodsInfo) return;
        if (!isUndefined(goodsInfo.antibiotic) && this.innerState.allowAntibiotic) {
            if (!this.innerState.allowAntibioticList?.includes(goodsInfo.antibiotic ?? 0)) {
                return showConfirmDialog(
                    "提示",
                    `【${goodsInfo.displayName}】为${goodsInfo._antibioticDisplay}药物，当前医生不具备使用权限`
                );
            }
        }
        goodsInfo.keyId = goodsInfo.keyId ?? UUIDGen.generate();
        if (!this.innerState.canCheckWithoutStock && (!goodsInfo.inStock || (goodsInfo.getStockPieceCount() ?? 0) < 0)) return;
        const medicine = JsonMapper.deserialize(GoodsInfo, goodsInfo);
        const _addFormItem = JsonMapper.deserialize(PrescriptionFormItem, {
            keyId: goodsInfo.keyId,
            groupId: event.group ?? ChargeUtils.maxGroupId,
            goodsId: medicine.id,
            productInfo: medicine,
            ...goodsInfo,
            name: goodsInfo.displayName, // 中药药房切换需要用到name进行筛选
        });
        const sameGroup = this.innerState.getPrescriptionItemListWithGroupId(_addFormItem.groupId);
        if (
            !!sameGroup.find(
                (formItem) =>
                    (!!formItem?.keyId && !!_addFormItem?.keyId && _addFormItem.keyId == formItem.keyId) ||
                    (formItem.goodsInfo.id == _addFormItem.goodsInfo.id && formItem.displayName == _addFormItem.displayName)
            )
        ) {
            Toast.show(`${medicine.displayName}已添加`, { warning: true, autoBlurText: false });
            this._searchInput?.focus();
            return;
        }
        await this._initMedicineUsage(_addFormItem);
        await MedicineAddPageUtils.computeMedicineUnitCount(_addFormItem);
        this.innerState.formDetail.prescriptionFormItems?.push(_addFormItem);
        this.innerState.currentFocusItem = _addFormItem.keyId;
        //更新页面顺序
        this.innerState.computedPrescriptionItemListSort();
        this.update();
    }

    @actionEvent(_EventRecordPartLayout)
    async *_mapEventRecordPartLayout(event: _EventRecordPartLayout): AsyncGenerator<State> {
        if (_.isNaN(event.layoutY)) return;
        this.innerState.layoutState.set(event.key, event.layoutY);
    }

    @actionEvent(_EventUpdateWesternMedicineGroupIndex)
    async *_mapEventUpdateWesternMedicineGroupIndex(event: _EventUpdateWesternMedicineGroupIndex): AsyncGenerator<State> {
        const { formItem } = event,
            groupId = formItem.groupId;
        this.innerState.currentFocusGroupId = groupId;
        yield this.innerState;
        const _options: string[] = _.range(1, 10).map((item) => "组" + item.toString());
        _options.push("-"); //增加不分组的选项
        const _initIndex = groupId ? (groupId == ChargeUtils.maxGroupId ? _options.length - 1 : groupId - 1) : undefined;
        const selects = await AbcDialog.showOptionsBottomSheet({
            title: "选择分组",
            options: _options,
            initialSelectIndexes: _.isUndefined(_initIndex) ? _initIndex : new Set([_initIndex]),
        });
        this.innerState.currentFocusGroupId = undefined;
        yield this.innerState;
        //解决没有选择分组或者选择同一组，选中状态没有变化，而选择了其他不同分组，则不需要update，否则会导致页面闪烁
        if (!selects?.length || _initIndex == selects![0]) {
            return;
        }
        if (selects![0] + 1 != groupId) {
            const currentGroupId = _options[selects![0]] == "-" ? ChargeUtils.maxGroupId : selects![0] + 1;
            formItem.groupId = currentGroupId;
            //更改组别后 用法、频率、天数与组别中的第一项相同
            const groups = this.innerState.getPrescriptionItemListWithGroupId(currentGroupId);
            if (!!groups?.length) {
                const firstPrescription = groups[0];
                Object.assign(formItem, {
                    freq: firstPrescription.freq,
                    days: firstPrescription.days,
                    usage: firstPrescription.usage,
                });
                MedicineAddPageUtils.computeMedicineUnitCount(firstPrescription);
            }
            //同一组别相互影响，不同组别相互无影响
            //判断是否存在相同组别，需要过滤掉当前正在编辑项（当前编辑组的selectedMedicines无值）/如果选择的是不分组也需要过滤
            this.innerState.computedPrescriptionItemListSort();
            this.update();
        }
    }

    @actionEvent(_EventResetWesternAstt)
    async *_mapEventResetWesternAstt(event: _EventResetWesternAstt): AsyncGenerator<State> {
        event.formItem.ast = null;
        this.update();
    }

    @actionEvent(_EventDeleteWesternMedicineGroupIndex)
    async *_mapEventDeleteWesternMedicineGroupIndex(): AsyncGenerator<State> {
        this.innerState.isEditGroup = false;
        //取消分组后，各个药品之间没有联动关系
        this.innerState.formDetail.prescriptionFormItems?.forEach((item) => {
            item.groupId = ChargeUtils.maxGroupId;
        });
        this.update();
    }

    @actionEvent(_EventModifyEditGroupState)
    async *_mapEventModifyEditGroupState(event: _EventModifyEditGroupState): AsyncGenerator<State> {
        this.innerState.isEditGroup = event.status;
        this.update();
    }

    private async _updatePsychotropicNarcoticEmployee(): Promise<void> {
        const result: { patientInfo?: Patient; agentInfo?: Patient } = await ABCNavigator.navigateToPage(
            <JingMaPrescriptionRequiredPage
                patientInfo={this.innerState.outpatientSheetDetail?.patient}
                agentInfo={this.innerState.outpatientSheetDetail?.psychotropicNarcoticEmployee}
            />
        );
        if (!result) return;
        this.innerState.outpatientSheetDetail!.patient = JsonMapper.deserialize(Patient, result?.patientInfo);
        this.innerState.outpatientSheetDetail!.psychotropicNarcoticEmployee = JsonMapper.deserialize(PsychotropicNarcoticEmployee, {
            ...result?.agentInfo,
            patient: result?.patientInfo,
        });
    }
    @actionEvent(_EventModifyPrescriptionNarcoticType)
    async *_mapEventModifyPrescriptionNarcoticType(event: _EventModifyPrescriptionNarcoticType): AsyncGenerator<State> {
        this.innerState.formDetail.psychotropicNarcoticType = event.type;
        const needCheckType = [
            PsychotropicNarcoticTypeEnum.JING1,
            PsychotropicNarcoticTypeEnum.JING2,
            PsychotropicNarcoticTypeEnum.MAZUI,
            PsychotropicNarcoticTypeEnum.DU,
        ];
        // 处方类型为：麻醉、精一、精二、毒中任何一种
        if (event?.type && needCheckType.includes(event.type)) {
            await this._updatePsychotropicNarcoticEmployee();
        }
        this.update();
    }

    @actionEvent(_EventModifyPrescriptionType)
    async *_mapEventModifyPrescriptionType(): AsyncGenerator<State> {
        let psychotropicTypeList = PsychotropicNarcoticTypeList;
        if (this.innerState.isChinesePrescription || this.innerState.isExamination) {
            psychotropicTypeList = psychotropicTypeList.filter((item) => item.value == 0 || item.value > 4);
        }
        const result: { type?: number; isModify?: boolean } = await showBottomPanel(
            <PrescriptionTypeDialog
                list={psychotropicTypeList}
                type={this.innerState.formDetail.psychotropicNarcoticType}
                agentName={this.innerState.outpatientSheetDetail?.psychotropicNarcoticEmployee?.name}
                isShowAgent={
                    !!this.innerState.formDetail?.psychotropicNarcoticType &&
                    this.innerState.formDetail?.psychotropicNarcoticType <= PsychotropicNarcoticTypeEnum.DU
                }
            />,
            {
                topMaskHeight: pxToDp(312),
            }
        );
        if (!result) return;
        if (!isNil(result.type)) {
            this.requestModifyPrescriptionNarcoticType(result.type ?? 0);
        }
        if (!!result?.isModify) {
            await this._updatePsychotropicNarcoticEmployee();
        }
    }

    @actionEvent(_EventDeleteWesternRemark)
    async *_mapEventDeleteWesternRemark(event: _EventDeleteWesternRemark): AsyncGenerator<State> {
        event.formItem.specialRequirement = undefined;
        // 如果备注是自备，除了清空备注之外，还需要将chargeType恢复成默认
        event.formItem.chargeType = DispensingFormItemsSourceItemType.default;
        this.update();
    }

    @actionEvent(_EventDeleteMedicine)
    async *_mapEventDeleteMedicine(event: _EventDeleteMedicine): AsyncGenerator<State> {
        const formDetail = this.innerState.formDetail as PrescriptionChineseForm;
        formDetail.prescriptionFormItems?.forEach((item, index) => {
            if (item.keyId == event.formItem.keyId) {
                this.innerState.formDetail.prescriptionFormItems?.splice(index, 1);
            }
        });
        this.innerState.currentFocusItem = null;
        if (!this.innerState.isEditGroup) this._searchInput?.focus();
        //如果此时为多药房，并且输入框无值，则类型切换应该恢复成当前选择类型
        if (this.innerState.pharmacyInfoConfig?.isOpenMultiplePharmacy) {
            this.innerState.searchChineseSpecType = this.innerState.chineseMedicineSpecType;
        }
        // 空中药房，代煎、颗粒需要调用算费
        if (
            formDetail?.pharmacyType == PharmacyType.air &&
            (formDetail.medicineStateScopeId == MedicineScopeId.keLi || formDetail.medicineStateScopeId == MedicineScopeId.daiJian)
        ) {
            this._airPharmacyCalculateTrigger.next();
        }
        this.update();
    }

    @actionEvent(_EventUpdateWesternMedicineUsage)
    async *_mapEventUpdateWesternMedicineUsage(event: _EventUpdateWesternMedicineUsage): AsyncGenerator<State> {
        const { formItem, value } = event,
            { groupId = ChargeUtils.maxGroupId } = formItem;
        if (groupId == ChargeUtils.maxGroupId || !groupId) {
            formItem.usage = value;
            // 如果从滴眼、滴鼻、滴耳切换到其它用法需重置剂量单位
            if (
                (formItem.inputUsages?.usage?.name == "滴眼" ||
                    formItem.inputUsages?.usage?.name == "滴鼻" ||
                    formItem.inputUsages?.usage?.name == "滴耳") &&
                formItem.usage !== "滴眼" &&
                formItem.usage !== "滴鼻" &&
                formItem.usage !== "滴耳"
            ) {
                formItem.dosageUnit = ABCUtils.first(formItem.goodsInfo.usageUnits);
            }
            await MedicineAddPageUtils.computeMedicineUnitCount(formItem);
        } else {
            const groups = this.innerState.getPrescriptionItemListWithGroupId(groupId);
            for (const item of groups) {
                item.usage = value;
                await MedicineAddPageUtils.computeMedicineUnitCount(item);
            }
        }
        const { infusionFormUsageGroup, isInfusionPrescription } = this.innerState;
        if (isInfusionPrescription) {
            const groupUsage = infusionFormUsageGroup.get(groupId);
            if (!groupUsage) return;
            groupUsage.usage = value;
            infusionFormUsageGroup.get(groupId);
        }
        this._clearAdjustmentFee(formItem);
        this.update();
    }

    @actionEvent(_EventUpdateWesternMedicineFreq)
    async *_mapEventUpdateWesternMedicineFreq(event: _EventUpdateWesternMedicineFreq): AsyncGenerator<State> {
        const { formItem, value } = event,
            { groupId = ChargeUtils.maxGroupId } = formItem;
        if (groupId == ChargeUtils.maxGroupId || !groupId) {
            formItem.freq = value;
            await MedicineAddPageUtils.computeMedicineUnitCount(formItem);
        } else {
            const groups = this.innerState.getPrescriptionItemListWithGroupId(groupId);
            for (const item of groups) {
                item.freq = value;
                await MedicineAddPageUtils.computeMedicineUnitCount(item);
            }
        }
        const { infusionFormUsageGroup, isInfusionPrescription } = this.innerState;
        if (isInfusionPrescription) {
            const groupUsage = infusionFormUsageGroup.get(groupId);
            if (!groupUsage) return;
            groupUsage.freq = value;
            infusionFormUsageGroup.get(groupId);
        }
        this._clearAdjustmentFee(formItem);
        this.update();
    }

    @actionEvent(_EventUpdateWesternDosageCount)
    async *_mapEventUpdateWesternDosageCount(event: _EventUpdateWesternDosageCount): AsyncGenerator<State> {
        const { formItem, value } = event;
        formItem.dosage = value;
        await MedicineAddPageUtils.computeMedicineUnitCount(formItem);
        this._clearAdjustmentFee(formItem);
        this.update();
    }

    @actionEvent(_EventUpdateDosageUnit)
    async *_mapEventUpdateDosageUnit(event: _EventUpdateDosageUnit): AsyncGenerator<State> {
        const { formItem, value } = event;
        formItem.dosageUnit = value;
        await MedicineAddPageUtils.computeMedicineUnitCount(formItem);
        this._clearAdjustmentFee(formItem);
        this.update();
    }

    @actionEvent(_EventUpdateWesternMedicineDays)
    async *_mapEventUpdateWesternMedicineDays(event: _EventUpdateWesternMedicineDays): AsyncGenerator<State> {
        const { formItem, value } = event,
            { groupId = ChargeUtils.maxGroupId } = formItem;
        if (groupId == ChargeUtils.maxGroupId || !groupId) {
            formItem.days = value ? Number(value || 0) : undefined;
            await MedicineAddPageUtils.computeMedicineUnitCount(formItem);
        } else {
            const groups = this.innerState.getPrescriptionItemListWithGroupId(groupId);
            for (const item of groups) {
                item.days = value ? Number(value || 0) : undefined;
                await MedicineAddPageUtils.computeMedicineUnitCount(item);
            }
        }
        const { infusionFormUsageGroup, isInfusionPrescription } = this.innerState;
        if (isInfusionPrescription) {
            const groupUsage = infusionFormUsageGroup.get(groupId);
            if (!groupUsage) return;
            groupUsage.days = Number(value || 0);
            infusionFormUsageGroup.set(groupId, groupUsage);
        }
        this._clearAdjustmentFee(formItem);
        this.update();
    }

    @actionEvent(_EventUpdateWesternAmountUnitCount)
    async *_mapEventUpdateWesternAmountUnitCount(event: _EventUpdateWesternAmountUnitCount): AsyncGenerator<State> {
        const { formItem, value } = event;
        formItem.unitCount = !!value ? Number(value) : undefined;
        this._clearAdjustmentFee(formItem);
        this.update();
    }

    @actionEvent(_EventUpdateWesternAmountUnit)
    async *_mapEventUpdateWesternAmountUnit(event: _EventUpdateWesternAmountUnit): AsyncGenerator<State> {
        const { formItem, value } = event;
        formItem!.unit = value;
        this._clearAdjustmentFee(formItem);
        this.update();
    }

    @actionEvent(_EventContinueSearch)
    async *_mapEventContinueSearch(event: _EventContinueSearch): AsyncGenerator<State> {
        delayed(100).subscribe(() => {
            if (event.focus) {
                this._searchInput?.focus();
            }
            this.innerState.currentFocusItem = null;
        });
        //在空中药房状态下，特定药态对应计算服用天数
        this.computedAirPharmacyUSagDays();
        this.update();
    }

    @actionEvent(_EventSubmit)
    async *_mapEventSubmit(/*event: _EventSubmit*/): AsyncGenerator<State> {
        const formDetail = this.innerState.formDetail;
        if (this.innerState.isWesternPrescription) {
            for (const formItem of formDetail.prescriptionFormItems ?? []) {
                const { usage, freq, dosage, dosageUnit, unitCount, unit, days } = formItem;
                if (!usage || !freq || (dosageUnit == "适量" ? false : dosage == null) || !dosageUnit || !unitCount || !unit || !days) {
                    await Toast.show("请完善必填项", { warning: true });
                    this.innerState.currentFocusItem = formItem.keyId;
                    this.innerState.showErrorHint = true;
                    yield ScrollToFocusItemState.fromState(this.innerState);
                    return;
                }
            }
        } else if (this.innerState.isChinesePrescription) {
            //需要将medicineAutoFocus设置为false,避免在聚焦药品克数的时候，直接点击完成，造成提示焦点错位
            for (const formItem of formDetail.prescriptionFormItems ?? []) {
                const { doseCount, eqConversionRule } = formDetail;
                const { unitCount, eqUnitCount } = formItem;
                const { typeId, pieceUnit } = formItem.goodsInfo;
                const isMedicineChineseGranule = GoodsTypeId.medicineChineseGranule === typeId;

                let tempUnitCount = unitCount;
                // 中药颗粒 & 单位为g/克
                if (
                    userCenter.enableEqCoefficient &&
                    isMedicineChineseGranule &&
                    (pieceUnit === "g" || pieceUnit === "克") &&
                    eqConversionRule === ChineseMedicineSpecType.chinesePiece
                ) {
                    tempUnitCount = eqUnitCount;
                }
                if (!tempUnitCount || (!this.isHideProcessInfo && !doseCount)) {
                    await Toast.show("请完善必填项", { warning: true });
                    this.innerState.currentFocusItem = formItem.keyId;
                    this.innerState.showErrorHint = true;
                    yield ScrollToFocusItemState.fromState(this.innerState);
                    return;
                }
            }
            if (!this._showMinimumLimitTips()) return;
        } else if (this.innerState.isInfusionPrescription) {
            for (const formItem of formDetail.prescriptionFormItems ?? []) {
                const { usage, freq, dosage, dosageUnit, unitCount, unit, days } = formItem;
                if (!usage || !freq || (dosageUnit == "适量" ? false : dosage == null) || !dosageUnit || !unitCount || !unit || !days) {
                    await Toast.show("请完善必填项", { warning: true });
                    this.innerState.currentFocusItem = formItem.keyId;
                    this.innerState.showErrorHint = true;
                    yield ScrollToFocusItemState.fromState(this.innerState);
                    return;
                }
            }
        }
        //中药处方返回之前，需要匹配当前选中药房信息（selectPharmacyInfo在使用中只针对本地多药房，而代煎代配、空中药房没有用到，此时需要回显，否则一直都是本地药房）
        //解决：诊选择处方模板后，修改为代煎代配药房，收费完成后在药房发药时提示“不同药房类型发药
        if (this.innerState.isChinesePrescription) {
            for (const formItem of formDetail.prescriptionFormItems ?? []) {
                const { pharmacyType, pharmacyNo } = formItem;
                if (pharmacyType != this.innerState.selectPharmacyInfo?.type && pharmacyNo != this.innerState.selectPharmacyInfo?.no) {
                    // this.innerState.selectPharmacyInfo = this.innerState.pharmacyInfoConfig?.pharmacyList?.find(
                    //     (t) => t.type == pharmacyType && t.no == pharmacyNo
                    // );
                }
            }
        }
        ABCNavigator.pop({
            formData: this.formatSubmitFormDetail(formDetail),
            extraParameter: {
                patient: this.innerState.outpatientSheetDetail?.patient,
                psychotropicNarcoticEmployee: this.innerState.outpatientSheetDetail?.psychotropicNarcoticEmployee,
            },
        });
    }

    async _loadVendor(form: PrescriptionChineseForm): Promise<AirPharmacyVendor[] | ABCError> {
        try {
            return ChargeAgent.getVendors({
                airPharmacyFormItems: form.prescriptionFormItems,
                doseCount: form.doseCount!,
                goodsTypeId: ChineseMedicineSpecType.typeFromName(form.specification!)
                    ? ChineseGoodType.chineseGranule
                    : ChineseGoodType.chinesePiece,
                medicineStateScopeId: form.medicineStateScopeId!,
                usageScopeId: form.usageScopeId!,
                vendorId: form.vendorId!,
                pharmacyNo: form.pharmacyNo,
            });
        } catch (error) {
            return new ABCError(error);
        }
    }

    @actionEvent(_EventCopyTemplate)
    async *_mapEventCopyTemplate(event: _EventCopyTemplate): AsyncGenerator<State> {
        const tp = await OutpatientPrescriptionTemplateSearchDialog.show(false, {
            filterType: event.type,
            chineseUseFill: event.type != MedicineAddType.chinese,
        });
        if (!tp) return;
        const refreshResult = await OutpatientUtils.refreshGoodsInfo(
            [
                ...(tp.prInfo?.prescriptionWesternForms ?? []),
                ...(tp.prInfo?.prescriptionChineseForms?.filter((form) => form.pharmacyType != PharmacyType.air) ?? []),
                ...(tp.prInfo?.prescriptionInfusionForms ?? []),
                ...(tp.prInfo?.prescriptionExternalForms ?? []),
            ],
            this._searchClinicId ?? "",
            true,
            this.innerState.selectPharmacyInfo?.no
        );
        //中药的空中药房
        if (this.innerState.isChinesePrescription && this.innerState.pharmacy == PharmacyType.air) {
            tp.prInfo?.prescriptionChineseForms
                ?.filter((form) => form.pharmacyType == PharmacyType.air)
                .forEach((form) => {
                    this._loadVendor(form).then((rsp) => {
                        if (rsp instanceof ABCError) return;
                        if (rsp) {
                            rsp.forEach((item) => {
                                if (item.checked) {
                                    const _orderItems = item.orderItems?.map((item) => JsonMapper.deserialize(PrescriptionFormItem, item));
                                    form.vendor = item;
                                    //最新供应商基础信息赋值到处方单上
                                    form.totalPrice = item.totalPrice;
                                    form.processPrice = item.processPrice;
                                    form.vendorName = item.vendorName;
                                    form.vendorId = item.vendorId;
                                    form.vendorUsageScopeId = item.vendorUsageScopeId;

                                    for (const formItem of _orderItems ?? []) {
                                        if (!formItem) continue;
                                        formItem.goodsId = formItem.productInfo?.id;
                                        formItem.productId = formItem.productInfo?.id;
                                        formItem.type = formItem.productInfo?.type;
                                        formItem.subType = formItem.productInfo?.subType;
                                        formItem.name = formItem.productInfo?.displayName ?? formItem.name;
                                        formItem.unit = formItem.unit ?? "g";
                                        formItem.medicineCadn = formItem.productInfo?.medicineCadn;
                                        formItem.productInfo = formItem.productInfo;
                                        const _formItem = _.findIndex(
                                            form.prescriptionFormItems,
                                            (i) => i.displayName == formItem.displayName
                                        );
                                        if (_.isNumber(_formItem)) {
                                            form.prescriptionFormItems![_formItem] = _.merge(
                                                form.prescriptionFormItems![_formItem],
                                                formItem
                                            );
                                        } else {
                                            form!.prescriptionFormItems?.push(formItem);
                                        }
                                    }
                                    this._modifyChinesePrescriptionUsage(form);
                                }
                            });
                            this.update();
                        }
                    });
                });
        }
        if (!(refreshResult instanceof Array)) {
            return await Toast.show(`添加失败 ${errorToStr(refreshResult)}`, {
                warning: true,
            });
        }

        if (event.type == MedicineAddType.western) {
            let _addType = MedicineTemplateAddType.reset;
            const oldGroup = this.innerState.formDetail;
            if (!!oldGroup.prescriptionFormItems?.length) {
                _addType = await MedicineTemplateAddTypeDialog.show({
                    title: tp.name,
                    content: "确认覆盖当前处方",
                    buttonType: [
                        { name: "确定", value: MedicineTemplateAddType.reset },
                        { name: "取消", value: MedicineTemplateAddType.cancel },
                    ],
                });
            }
            if (!_addType) return;

            if (!!refreshResult.length) {
                for (const form of refreshResult ?? []) {
                    //将复制模板数据组装成页面需要的格式
                    this.innerState.formDetail = form;
                }
            }
            // 成药处方包含多个分组时可编辑
            [...this.innerState.getPrescriptionItemListChunkGroupId().keys()].some((id) => id != ChargeUtils.maxGroupId) &&
                (this.innerState.isEditGroup = true);
        } else if (event.type == MedicineAddType.chinese) {
            // 中药处方模板复制规则：
            // 无药，以模板为准：
            // 有药，添加以原有药房为准；覆盖以模板为准
            let _addType = MedicineTemplateAddType.reset;
            const oldGroup = this.innerState.formDetail;
            oldGroup.prescriptionFormItems?.forEach((item) => {
                item.cMSpec = !!item.cMSpec ? item.cMSpec : ChineseMedicineSpecType.fullNames()[this.innerState.chineseMedicineSpecType];
            });
            if (!!oldGroup.prescriptionFormItems?.length) {
                _addType = await MedicineTemplateAddTypeDialog.show({
                    title: tp.name ?? "调用模板",
                    content: "作为汤头添加到当前处方，还是覆盖当前处方",
                    buttonType: [
                        { name: "添加", value: MedicineTemplateAddType.push },
                        { name: "覆盖", value: MedicineTemplateAddType.reset },
                    ],
                });
            }
            if (!_addType) return;

            let medicines: Array<PrescriptionFormItem> = [];

            //是否采取当前药房类型（true:采用当前药房类型，false:采用导入模板药房类型）
            //经典方剂和临床验方的pharmacyType没有类型，所以采用当前本地的类型
            const isUseLocalType =
                (!!oldGroup.prescriptionFormItems?.length && _addType == MedicineTemplateAddType.push) ||
                tp.category == PrescriptionTemplateCategory.classical ||
                tp.category == PrescriptionTemplateCategory.clinical;

            //模板处方药品类型
            const type = !!refreshResult?.[0]?.specification
                ? refreshResult?.[0]?.specification == "中药饮片"
                    ? 0
                    : 1
                : this.innerState.chineseMedicineSpecType;
            if (_addType == MedicineTemplateAddType.push) {
                //追加的时候需要与本地药品的类型比对,如果不同，先进行类型转换(以当前存在类型为准，即将模板类型转换为当前显示的类型),必须先转换本地药品类型，然后再追加模板药品
                await this._trans(
                    new _EventChineseMedicineSpecType(
                        this.innerState.chineseMedicineSpecType,
                        this.innerState.pharmacy != PharmacyType.normal || this.innerState.unableChinesePrescriptionSupportMix,
                        this.innerState.pharmacy
                    )
                );

                medicines = oldGroup.prescriptionFormItems ?? [];
            } else {
                //覆盖的时候也需要对比当前类型（饮片、颗粒）,如果不同，则需要修改药品类型
                this.innerState.formDetail.specification = ChineseMedicineSpecType.displayNames()[type];
            }

            if (!_.isEmpty(refreshResult)) {
                for (const form of refreshResult) {
                    if (!_.isEmpty(form.prescriptionFormItems)) {
                        for (const formItem of form.prescriptionFormItems!) {
                            formItem.cMSpec = !!formItem.cMSpec ? formItem.cMSpec : refreshResult[0].specification;
                            const medicine = formItem.goodsInfo;
                            medicine.keyId = medicine.keyId ?? medicine.compareKey();
                            const isExist = medicines.find((t) =>
                                !!medicine.id && t.id
                                    ? t.id == medicine.id && t.displayName == medicine.displayName
                                    : t.displayName == medicine.displayName
                            );
                            if (!isExist) {
                                //避免药品没有cMSpec,进行类型转换时误判
                                medicine.cMSpec = !!medicine.cMSpec ? medicine.cMSpec : formItem.cMSpec;
                                medicines.push(formItem);
                            }
                        }
                    }
                }
                // 判断当前类型，如果为本地药房，则不需要添加vendor，如果不是本地药房则需要
                const isAddVendor = (isUseLocalType ? this.innerState.pharmacy : refreshResult?.[0]?.pharmacyType) == PharmacyType.air;
                const chinesePrescriptionUsage = ChinesePrescriptionUsage.fromPrescriptionForm(refreshResult?.[0], isAddVendor);
                chinesePrescriptionUsage?.fillPrescriptionChineseForm(refreshResult?.[0]);
                if (isUseLocalType) {
                    Object.assign(refreshResult?.[0], this.innerState.formDetail);
                }
                !!refreshResult && (refreshResult[0].prescriptionFormItems = medicines);
                this.innerState.formDetail = refreshResult?.[0] ?? this.innerState.formDetail;
            }
        } else if (event.type == MedicineAddType.infusion) {
            let _addType = MedicineTemplateAddType.reset;
            const oldGroup = this.innerState.formDetail;
            if (!!oldGroup.prescriptionFormItems?.length) {
                _addType = await MedicineTemplateAddTypeDialog.show({
                    title: tp.name,
                    content: "确认覆盖当前处方",
                    buttonType: [
                        { name: "确定", value: MedicineTemplateAddType.reset },
                        { name: "取消", value: MedicineTemplateAddType.cancel },
                    ],
                });
            }
            if (!_addType) return;

            //收集药品信息和用法信息
            if (!_.isEmpty(refreshResult)) {
                for (const form of refreshResult ?? []) {
                    this.innerState.formDetail = form;
                }
            }
            // 解决有多个分组的情况下，复制模板后，分组信息丢失的问题
            let usage = JsonMapper.deserialize(
                InfusionPrescriptionUsage,
                this.innerState.westernMedicineConfig?.prescriptionInfusionDefaultValues
            );
            const prescriptionGroup = this.innerState.getPrescriptionItemListChunkGroupId();
            prescriptionGroup.forEach((prescriptionList, groupId) => {
                if (!!prescriptionList[0]) {
                    usage = InfusionPrescriptionUsage.fromPrescriptionFormItem(prescriptionList[0]);
                }
                this.innerState.infusionFormUsageGroup.set(groupId, usage);
            });
        }
        if (this.innerState.isChinesePrescription) {
            //所有药品拼接进groups中后，需要将当前所有药品，转换为当前指定的类型
            const pharmacyType = this.innerState.formDetail.pharmacyType ?? 0;
            await this._trans(new _EventChineseMedicineSpecType(this.innerState.chineseMedicineSpecType, true, pharmacyType));
            // 解决从模板复制后，再去添加药品，搜索的类型没有转换为当前的药品类型问题
            this.innerState.searchChineseSpecType = this.innerState.chineseMedicineSpecType;
            //空中药房
            if (pharmacyType == PharmacyType.air) {
                this._loadVendorList.next();
            }
            if (pharmacyType == PharmacyType.virtual) {
                this._loadVirtualVendorList.next();
            }
        }
        // 补充keyId,避免后续操作找不到keyId
        this.innerState.formDetail.keyId = this.innerState.formDetail.keyId ?? UUIDGen.generate();
        this.innerState.formDetail?.prescriptionFormItems?.forEach((item) => {
            item.keyId = item.keyId ?? UUIDGen.generate();
        });
        this._calculatePriceTrigger.next();
        this.update();
    }

    @actionEvent(_EventSaveTemplate)
    async *_mapEventSaveTemplate(): AsyncGenerator<State> {
        const formDetail = this.innerState.formDetail;
        if (!formDetail.prescriptionFormItems?.length) {
            return Toast.show("未添加任何项目", { warning: true });
        }
        const temp = JsonMapper.deserialize(PrescriptionTemplateInfo, {});
        switch (this.innerState.medicineType) {
            case MedicineAddType.chinese:
                temp.prescriptionChineseForms = [formDetail as PrescriptionChineseForm];
                break;
            case MedicineAddType.infusion:
                temp.prescriptionInfusionForms = [formDetail];
                break;
            case MedicineAddType.western:
                temp.prescriptionWesternForms = [formDetail];
                break;
        }
        await SaveTemplateDialog.show(temp);
    }

    @actionEvent(_EventChangeArrangement)
    async *_mapEventChangeArrangement(): AsyncGenerator<State> {
        this.innerState.arrangement = !this.innerState.arrangement;
        sharedPreferences.setInt(CHINESE_ARRANGEMENT, this.innerState.arrangement ? 0 : 1);
        this.update();
    }

    @actionEvent(_EventAddMedicineGroup)
    async *_EventAddMedicineGroup(): AsyncGenerator<State> {
        AbcTextInput.focusInput?.blur();
        const { isInfusionPrescription, infusionFormUsageGroup, westernMedicineConfig } = this.innerState;
        if (isInfusionPrescription) {
            infusionFormUsageGroup.set(
                infusionFormUsageGroup.size + 1,
                JsonMapper.deserialize(InfusionPrescriptionUsage, westernMedicineConfig?.prescriptionInfusionDefaultValues)
            );
        }
        this.update();
    }
    @actionEvent(_EventModifyPharmacyType)
    async *_mapEventNewModifyPharmacyType(): AsyncGenerator<State> {
        this.innerState.searchInfo = [];
        this.innerState.searchListNoStock = [];

        const _airPharmacy = new SelectAirPharmacy();
        const formDetail = this.innerState.formDetail as PrescriptionChineseForm;
        const {
            prescriptionFormItems = [],
            medicineStateScopeId,
            usageScopeId,
            pharmacyType,
            pharmacyNo,
            vendorId,
            vendorName,
            vendorUsageScopeId,
            doseCount,
            specification,
        } = formDetail;
        const specificationType = ChineseMedicineSpecType.typeFromName(specification);
        _airPharmacy.prescriptForms = prescriptionFormItems;
        //多药房
        const isMultiplePharmacy = this.innerState.pharmacyInfoConfig?.isOpenMultiplePharmacy;
        Object.assign(_airPharmacy, {
            medicineStateScopeId,
            usageScopeId,
            pharmacyType,
            pharmacyNo:
                isMultiplePharmacy && pharmacyType == PharmacyType.normal ? this.innerState.selectPharmacyInfo?.no : pharmacyNo ?? 0,
            vendorId,
            doseCount,
            goodsTypeId: specificationType ? ChineseGoodType.chineseGranule : ChineseGoodType.chinesePiece,
            vendorUsageScopeId,
            vendorName,
        });
        const _pharmacyInfo = await SelectAirPharmacyDialog.show({
            prescriptInfo: _.cloneDeep(_airPharmacy),
            chineseMedicineSpecType: this.innerState.chineseMedicineSpecType,
            enableChangePharmacy: this.innerState.ableChangePharmacyType,
            showVirtualPharmacy: this.innerState.isOpenVirtualPharmacy,
            isEditLocalPharmacy: this.innerState.isEditLocalPharmacy,
            ableUseAirPharmacy: this.innerState.ableUseAirPharmacy,
            medicineType: this.innerState.medicineType,
            showCoClinicPharmacy: this.innerState.isOpenCoClinic,
        });
        // 避免切换药房时,药房信息调用_trans不正确
        this.innerState.formDetail.pharmacyNo = _pharmacyInfo?.pharmacyNo;
        this.innerState.formDetail.pharmacyType = _pharmacyInfo?.pharmacyType;
        if (_pharmacyInfo) {
            // 药房类型不是本地药房, 清空eqConversionRule等效转换规则
            if (_pharmacyInfo?.pharmacyType !== PharmacyType.normal) {
                this.innerState.formDetail.eqConversionRule = undefined;
                this.innerState.formDetail.prescriptionFormItems = this.innerState.formDetail.prescriptionFormItems?.map((item) => {
                    Object.assign(item, {
                        eqCoefficient: undefined,
                        eqUnitCount: undefined,
                        unitCount: item?.eqUnitCount || item?.unitCount,
                    });
                    return item;
                });
            }
            const isCoClinicPharmacy = _pharmacyInfo.pharmacyType == PharmacyType.coClinic;
            if (isCoClinicPharmacy) {
                // 所有处方处理是一样的
                Object.assign(this.innerState.formDetail, {
                    vendorId: undefined,
                    vendorName: undefined,
                    vendor: undefined,
                    usageScopeId: undefined,
                    medicineStateScopeId: undefined,
                });
                if (_airPharmacy.pharmacyNo != _pharmacyInfo.multiplePharmacyInfo?.no) {
                    this.innerState.selectPharmacyInfo = _pharmacyInfo?.multiplePharmacyInfo;
                    if (this.innerState.isChinesePrescription) {
                        this.innerState.formDetail.specification = "中药饮片";
                    }
                }
                if (!_pharmacyInfo.multiplePharmacyInfo) {
                    //清空当前的药房信息
                    this.update();
                    return;
                }
                if (!!this.innerState.formDetail.prescriptionFormItems?.length) {
                    const rsp = await MedicineAddPageUtils.computeFormGoodsItemStockInfoWithPharmacy([this.innerState.formDetail]);
                    if (rsp) {
                        this.innerState.formDetail = rsp[0];
                    }
                    this.update();
                }
                this.update();
                //结束————————
                return;
            } else {
                if (!this.innerState.isChinesePrescription) {
                    this.innerState.selectPharmacyInfo = _pharmacyInfo?.multiplePharmacyInfo;
                    //结束————————
                    if (!!this.innerState.formDetail.prescriptionFormItems?.length) {
                        const rsp = await MedicineAddPageUtils.computeFormGoodsItemStockInfoWithPharmacy([this.innerState.formDetail]);
                        if (rsp) {
                            this.innerState.formDetail = rsp[0];
                        }
                    }
                    this.update();
                    return;
                }
            }
            //本地药房
            let tranStatus = true;
            if (_pharmacyInfo?.pharmacyType == 0) {
                //如果为多药房，切换了药房，需要查询库存信息，如果只是切换了饮片/颗粒类型,则只改变类型
                //多药房只切换了药房，类型没有切换，需要强制查询一次库存
                const forceTrans = isMultiplePharmacy && specificationType == _pharmacyInfo?.prescriptFormType;
                if (isMultiplePharmacy) {
                    if (_airPharmacy.pharmacyNo != _pharmacyInfo.multiplePharmacyInfo?.no) {
                        this.innerState.selectPharmacyInfo = _pharmacyInfo?.multiplePharmacyInfo;
                    }
                }

                //空中药房/代煎代配转换为本地药房，需要强制走一遍转换(!!_airPharmacy.pharmacyType是因为没有开启空中药房或者代煎代配时,pharmacyType为undefined)
                tranStatus = await this._trans(
                    new _EventChineseMedicineSpecType(
                        _pharmacyInfo!.localPharmacyType!,
                        (!!_airPharmacy.pharmacyType &&
                            _airPharmacy.pharmacyType != PharmacyType.normal &&
                            _pharmacyInfo.pharmacyType == PharmacyType.normal) ||
                            forceTrans,
                        _pharmacyInfo.pharmacyType
                    )
                );
            }
            //代煎代配需查询一次药品信息
            if (_pharmacyInfo.pharmacyType == PharmacyType.virtual) {
                this.requestChineseMedicineSpecType(
                    _pharmacyInfo?.prescriptFormType ?? this.innerState.chineseMedicineSpecType,
                    true,
                    _pharmacyInfo.pharmacyType
                );
            }
            if (!tranStatus) return;
            const formDetail = this.innerState.formDetail as PrescriptionChineseForm;
            const { prescriptionFormItems = [] } = formDetail;
            if (!!_pharmacyInfo.prescriptForms?.length) {
                _pharmacyInfo.prescriptForms.forEach((formItem: PrescriptionFormItem, index: number) => {
                    //如何判断是同一个药品？?
                    //本地药房转空中药房后，没办法通过keyId进行比较；
                    // 在空中药房转换为本地药房时，如果是相同药品，只通过名字比较则会一直匹配第一个药品，导致克数丢失，如果添加了一个对应克数的比较
                    const _sameIndex = _.findIndex(
                        prescriptionFormItems,
                        (i, index) =>
                            (!!i.keyId && !!formItem.keyId && (i.keyId == formItem.keyId || i.goodsInfo.keyId == formDetail.keyId)) ||
                            (i.displayName == formItem.displayName && prescriptionFormItems[index].unitCount == formItem.unitCount)
                    );
                    !!formItem.productInfo && (formItem.productInfo.keyId = formItem?.keyId ?? UUIDGen.generate());
                    if (_sameIndex < 0) {
                        //空中药房药品列表返回的数据与本地
                        prescriptionFormItems[index] = formItem;
                    } else {
                        if (_pharmacyInfo.pharmacyType == PharmacyType.virtual || _pharmacyInfo.pharmacyType == PharmacyType.air) {
                            prescriptionFormItems[_sameIndex].productInfo = formItem.goodsInfo;
                        }
                    }
                });
            }
            formDetail.vendor = _pharmacyInfo?.vendor;
            Object.assign(formDetail, {
                doseCount: _pharmacyInfo.doseCount,
                pharmacyType: _pharmacyInfo.pharmacyType,
                pharmacyNo:
                    isMultiplePharmacy && _pharmacyInfo.pharmacyType == PharmacyType.normal
                        ? _pharmacyInfo.multiplePharmacyInfo?.no
                        : _pharmacyInfo?.pharmacyNo,
                usageScopeId: _pharmacyInfo?.usageScopeId,
                medicineStateScopeId: !!_pharmacyInfo.medicineStateScopeId
                    ? _pharmacyInfo.medicineStateScopeId
                    : _pharmacyInfo.vendor?.vendorAvailableMedicalStates?.[0]?.medicalStatId,
                vendorId: _pharmacyInfo.vendor?.vendorId,
                vendorName: _pharmacyInfo.vendor?.vendorName,
                vendorUsageScopeId: _pharmacyInfo.vendor?.vendorUsageScopeId,
            });
            const _specificationType = _pharmacyInfo?.prescriptFormType;
            if (!_.isUndefined(_specificationType) && specificationType != _specificationType) {
                formDetail.specification = ChineseMedicineSpecType.fullNames()[_specificationType];
                this.innerState.searchChineseSpecType = this.innerState.chineseMedicineSpecType;
            }
            //空中药房需要记忆上次用法用量(如果当前供应商没有该用法用量，应该清空)
            const usageOptions = _pharmacyInfo.vendor?.businessScopeConfig?.usageOptions;
            const freqOptions = _pharmacyInfo.vendor?.businessScopeConfig?.freqOptions;
            const usageLevelOptions = _pharmacyInfo.vendor?.businessScopeConfig?.usageLevelOptions;
            const dosageOptions = _pharmacyInfo.vendor?.businessScopeConfig?.dosageOptions;
            let chinesePrescriptionUsage = !!clinicSharedPreferences.getObject(CHINESE_PRESCRIPTION_USAGE_DEFAULT)
                ? JSON.parse(clinicSharedPreferences.getObject(CHINESE_PRESCRIPTION_USAGE_DEFAULT))
                : undefined;
            chinesePrescriptionUsage = !!chinesePrescriptionUsage
                ? JsonMapper.deserialize(PrescriptionDefaultUsage, {
                      dailyDosage: !!dosageOptions?.find((t) => t?.name == chinesePrescriptionUsage?.dailyDosage?.name)
                          ? chinesePrescriptionUsage?.dailyDosage?.name
                          : undefined,
                      usage: !!usageOptions?.find((t) => t?.name == chinesePrescriptionUsage?.usage?.name)
                          ? chinesePrescriptionUsage?.usage?.name
                          : undefined,
                      freq: !!freqOptions?.find((t) => t?.name == chinesePrescriptionUsage?.freq?.name)
                          ? chinesePrescriptionUsage?.freq?.name
                          : undefined,
                      usageLevel: !!usageLevelOptions?.find((t) => t?.name == chinesePrescriptionUsage?.usageLevel?.name)
                          ? chinesePrescriptionUsage?.usageLevel?.name
                          : undefined,
                  })
                : undefined;
            const chineseUsageInfo =
                (_pharmacyInfo?.pharmacyType == PharmacyType.air && !!chinesePrescriptionUsage
                    ? chinesePrescriptionUsage
                    : ChineseMedicineConfigProvider.getChinesePRWithUsage(_pharmacyInfo?.usageScopeName ?? "")) ??
                ChineseMedicineConfigProvider.getChinesePRWithSpecification(
                    this.innerState.searchChineseSpecType == ChineseMedicineSpecType.chinesePiece
                        ? ChineseMedicineSpecType.chinesePiece
                        : ChineseMedicineSpecType.chineseGranule
                );
            // 需要给formDetail.usage重新赋值，否则切换类型，用法没有更新（本地药房，饮片默认用法煎服，颗粒默认用法冲服）
            formDetail.usage = chineseUsageInfo.usage;
            //本地药房服用量会根据用法以及饮片、颗粒而有所不同
            if (!_pharmacyInfo?.pharmacyType) {
                let usageLevelList: ChineseUsageItemInfo[] = [];
                const specificationType = this.innerState.chineseMedicineSpecType;
                const { usageLevel, zhiGaoUsageLevel, zhiWanUsageLevel, daFenUsageLevel, keLiChongFuUsageLevel } = ChineseMedicine;
                switch (chineseUsageInfo.usage) {
                    case "制膏":
                        usageLevelList = zhiGaoUsageLevel;
                        break;
                    case "制丸":
                        usageLevelList = zhiWanUsageLevel;
                        break;
                    case "打粉":
                        usageLevelList = daFenUsageLevel;
                        break;
                    default:
                        usageLevelList = usageLevel;
                }
                if (specificationType == ChineseMedicineSpecType.chineseGranule && chineseUsageInfo?.usage == "冲服") {
                    usageLevelList = keLiChongFuUsageLevel;
                }
                chineseUsageInfo.usageLevel =
                    usageLevelList.find((t) => t.name == chineseUsageInfo.usageLevel)?.name ??
                    ChineseMedicineConfigProvider.getChinesePRWithUsage(chineseUsageInfo?.usage)?.usageLevel;
            }
            this.computedAirPharmacyUSagDays();
            this.computedTisanesSack(true);
            this._calculatePriceTrigger.next();
            //切换饮片、颗粒时，对应的药态制法应该置空
            formDetail.processRemark = undefined;
            //空中药房供应商配置--颗粒剂
            if (_pharmacyInfo?.vendor) {
                this.innerState.currentAirPharmacyKeLiConfig = this.innerState.currentAirPharmacyKeLiConfig ?? new AirPharmacyKeLiConfig();
                this.innerState.currentAirPharmacyKeLiConfig.calculateProcessBagType = _pharmacyInfo.vendor?.calculateProcessBagType;
                this.innerState.currentAirPharmacyKeLiConfig.processBagUnitCount = _pharmacyInfo.vendor?.processBagUnitCount;
                this.innerState.currentAirPharmacyKeLiConfig.processBagUnit = _pharmacyInfo.vendor?.processBagUnit;
                this.innerState.currentAirPharmacyKeLiConfig.doseCountLimit = _pharmacyInfo.vendor?.doseCountLimit;
            }
            this.update();
        }
        this._searchInput?.focus();
    }

    @actionEvent(_EventAdjustDiscount)
    async *_mapEventAdjustDiscount(event: _EventAdjustDiscount): AsyncGenerator<State> {
        const { formItem, discount, type } = event;
        if (!formItem) return;
        Object.assign(formItem, {
            expectedTotalPriceRatio: discount / 100,
            expectedTotalPrice: undefined,
            expectedUnitPrice: undefined,
            totalPrice: undefined,
            unitAdjustmentFeeLastModifiedBy: userCenter.employee?.id ?? userCenter.employee?.employeeId,
        });
        if (type == "western") {
            this.innerState.outpatientSheetDetail!.prescriptionWesternForms = [this.innerState.formDetail];
        } else if (type == "infusion") {
            this.innerState.outpatientSheetDetail!.prescriptionInfusionForms = [this.innerState.formDetail];
        }
        this._calculatePriceTrigger.next(type);
        this.update();
    }

    @actionEvent(_EventAdjustTotalPrice)
    async *_mapEventAdjustTotalPrice(event: _EventAdjustTotalPrice): AsyncGenerator<State> {
        const { formItem, totalPrice, type } = event;
        if (!formItem) return;
        Object.assign(formItem, {
            expectedTotalPrice: totalPrice,
            expectedTotalPriceRatio: undefined,
            expectedUnitPrice: undefined,
            totalPriceRatio: undefined,
            unitAdjustmentFeeLastModifiedBy: userCenter.employee?.id ?? userCenter.employee?.employeeId,
        });
        if (type == "western") {
            this.innerState.outpatientSheetDetail!.prescriptionWesternForms = [this.innerState.formDetail];
        } else if (type == "infusion") {
            this.innerState.outpatientSheetDetail!.prescriptionInfusionForms = [this.innerState.formDetail];
        }
        this._calculatePriceTrigger.next(event.type);
        this.update();
    }

    @actionEvent(_EventCheckGoodsBatchInfo)
    async *_mapEventCheckGoodsBatchInfo(): AsyncGenerator<State> {
        const { prescriptionFormItems } = this.innerState.formDetail;
        const goodsBatchList: MedicineBatchInfoList[] = [];
        prescriptionFormItems?.forEach((formItem) => {
            const { unitCount, unit, specialRequirement, expectedTotalPrice, totalPrice, fractionPrice, goodsInfo, batchInfos } = formItem;
            let singlePrice = 0;
            if (!!unitCount && !!unit) {
                const unitPrice = goodsInfo.unitPriceWithUnit(unit);
                if (specialRequirement !== "【自备】") {
                    singlePrice = userCenter.clinic?.isDentistryClinic
                        ? expectedTotalPrice ?? totalPrice ?? unitCount * unitPrice + (fractionPrice ?? 0)
                        : unitCount * unitPrice + (fractionPrice ?? 0);
                }
                if (!!batchInfos?.length) {
                    goodsBatchList.push({
                        displayName: goodsInfo.displayName,
                        manufacturer: goodsInfo.manufacturer,
                        unit: goodsInfo.sellUnits[0],
                        count: unitCount,
                        totalPrice: singlePrice,
                        specifications: goodsInfo.displaySpec,
                        goodsBatchInfoList: batchInfos,
                    });
                }
            }
        });
        if (!goodsBatchList?.length) return;
        await showBottomPanel(<MedicineBatchInfoDialog batchList={goodsBatchList} />, {
            topMaskHeight: pxToDp(160),
        });
    }
    @actionEvent(_EventUpdateWesternMedicineAst)
    async *_mapEventUpdateWesternMedicineAst(event: _EventUpdateWesternMedicineAst): AsyncGenerator<State> {
        const { formItem, ast } = event;
        if (!!ast) {
            formItem.ast = ast;
            this.update();
        }
    }

    @actionEvent(_EventModifyMedicinePharmacy)
    async *_mapEventModifyMedicinePharmacy(event: _EventModifyMedicinePharmacy): AsyncGenerator<State> {
        const { formItem, pharmacy, isModifySelf } = event;

        //还原自备状态
        if (!isModifySelf) {
            this.requestModifyMedicineSelfProvidedStatus(formItem, DispensingFormItemsSourceItemType.default);
        }

        //修改库存信息
        const currentPharmacyStockInfo = formItem.goodsInfo.pharmacyGoodsStockList?.find((item) => item.pharmacyNo == pharmacy.no);
        const { stockPackageCount = 0, stockPieceCount = 0 } = currentPharmacyStockInfo ?? {};
        formItem.goodsInfo.stockPackageCount = stockPackageCount;
        formItem.goodsInfo.stockPieceCount = stockPieceCount;
        //修改药房信息
        formItem.goodsInfo.pharmacyNo = pharmacy.no;
        formItem.goodsInfo.pharmacyType = pharmacy.type;

        Object.assign(formItem, {
            pharmacyType: pharmacy.type,
            pharmacyNo: pharmacy.no,
            pharmacyName: pharmacy.name,
        });
        if (userCenter.clinic?.isDentistryClinic || userCenter.clinic?.isNormalHospital) this._clearAdjustmentFee(formItem, true);
        this.update();
    }

    @actionEvent(_EventModifyMedicineSelfProvidedStatus)
    async *_mapEventModifyMedicineSelfProvidedStatus(event: _EventModifyMedicineSelfProvidedStatus): AsyncGenerator<State> {
        const { formItem, status } = event;
        formItem.chargeType = status;
        //清空药房信息
        if (status == DispensingFormItemsSourceItemType.noCharge) {
            if (this.innerState.selectPharmacyInfo) {
                this.dispatch(new _EventModifyMedicinePharmacy(formItem, this.innerState.selectPharmacyInfo, true));
            }
        }
        this.update();
    }

    @actionEvent(_EventUpdateWesternRemark)
    async *_mapEventUpdateWesternRemark(event: _EventUpdateWesternRemark): AsyncGenerator<State> {
        const { formItem, value } = event;
        if (!!value) {
            formItem.specialRequirement = value;
            this.update();
        }
    }

    @actionEvent(_EventUpdateInfusionPrescriptionIvgtt)
    async *_mapEventUpdateInfusionPrescriptionIvgtt(event: _EventUpdateInfusionPrescriptionIvgtt): AsyncGenerator<State> {
        const { groupId, ivgtt } = event;
        //修改中间件
        const groupUsage = this.innerState.infusionFormUsageGroup.get(groupId);
        if (!groupUsage) return;
        groupUsage.ivgtt = ivgtt;
        //修改formItem
        const prescriptionItems = this.innerState.getPrescriptionItemListWithGroupId(groupId);
        prescriptionItems.forEach((formItem) => {
            formItem.ivgtt = ivgtt;
            formItem.ivgttUnit = groupUsage.ivgttUnit;
        });
        this.update();
    }

    @actionEvent(_EventDeleteGroup)
    async *_mapEventDeleteGroup(event: _EventDeleteGroup): AsyncGenerator<State> {
        const { westernMedicineConfig } = this.innerState;
        const { groupId } = event;
        //确认删除逻辑
        this.innerState.currentFocusItem = null;
        const prescriptionItemGroup = this.innerState.getPrescriptionItemListWithGroupId(groupId);
        const formDetail = this.innerState.formDetail;
        prescriptionItemGroup.forEach((formItem) => {
            formDetail.prescriptionFormItems?.forEach((item, index) => {
                if (item.keyId == formItem.keyId) {
                    this.innerState.formDetail.prescriptionFormItems?.splice(index, 1);
                }
            });
        });

        const infusionFormUsageGroup = this.innerState.infusionFormUsageGroup;
        if (infusionFormUsageGroup.size == 1) {
            infusionFormUsageGroup.set(
                groupId,
                JsonMapper.deserialize(InfusionPrescriptionUsage, westernMedicineConfig?.prescriptionInfusionDefaultValues)
            );
        } else {
            infusionFormUsageGroup.delete(groupId);
        }
        //重新刷新groupId 0开始自增
        const newInfusionFormUsageGroup = new Map();
        let sourceGroupId = 1;
        infusionFormUsageGroup.forEach((usage, groupId) => {
            const prescriptionItemGroup = this.innerState.getPrescriptionItemListWithGroupId(groupId);
            prescriptionItemGroup.forEach((formItem) => {
                formItem.groupId = sourceGroupId;
            });
            newInfusionFormUsageGroup.set(sourceGroupId, usage);
            sourceGroupId++;
        });
        this.innerState.infusionFormUsageGroup = newInfusionFormUsageGroup;
        this.update();
    }

    @actionEvent(_EventSearchGoods)
    async *_mapEventSearchGoods(event: _EventSearchGoods): AsyncGenerator<State> {
        this.innerState.keyword = event.keyword;
        if (this.innerState.isChinesePrescription && !this.innerState.keyword) return;
        if (this.innerState.isChinesePrescription) {
            //中药搜索默认清空上次内容
            this.innerState.loading = true;
            this.innerState.searchInfo = [];
            this.innerState.searchListNoStock = [];
            yield this.innerState;
        }
        this._searchGoodsInfoTrigger.next(0);
    }

    @actionEvent(_EventChineseSwitchType)
    async *_mapEventChineseSwitchType(): AsyncGenerator<State> {
        this.innerState.searchChineseSpecType = this.innerState.searchChineseSpecType == 0 ? 1 : 0;
        this._searchGoodsInfoTrigger.next();
    }

    @actionEvent(_EventUpdateChinesePrescriptionDosageAmount)
    async *_mapEventUpdateChinesePrescriptionDosageAmount(event: _EventUpdateChinesePrescriptionDosageAmount): AsyncGenerator<State> {
        this.innerState.formDetail.doseCount = Number(event.value);
        const { chineseMedicineSpecType } = this.innerState,
            formDetail = this.innerState.formDetail as PrescriptionChineseForm,
            { pharmacyType } = formDetail;
        if (pharmacyType && chineseMedicineSpecType == ChineseMedicineSpecType.chinesePiece) {
            if (pharmacyType == PharmacyType.air) {
                this._loadVendorList.next();
            } else if (pharmacyType == PharmacyType.virtual) {
                this._loadVirtualVendorList.next();
            }
        }
        this.computedTisanesSack(true).then(() => {
            // 需要更新页面显示中药加工信息
            if (formDetail.processName && formDetail.processName?.indexOf("煎药") > -1) {
                const usageStr = `${!!formDetail.processBagUnitCount ? "（1剂煎" + formDetail.processBagUnitCount + "袋" : ""}${
                    !!formDetail.totalProcessCount
                        ? "，共" + formDetail.totalProcessCount + "袋）"
                        : !!formDetail.processBagUnitCount
                        ? "）"
                        : ""
                }`;
                formDetail.processName = `煎药${usageStr}`;
            }
        });
        this._calculatePriceTrigger.next();
        this.update();
    }

    @actionEvent(_EventSelectChinesePrescriptionUsage)
    async *_mapEventSelectChinesePrescriptionUsage(event: _EventSelectChinesePrescriptionUsage): AsyncGenerator<State> {
        this.innerState.formDetail.usage = event.value;

        const formDetail = this.innerState.formDetail as PrescriptionChineseForm;
        const { usageScopeId, medicineStateScopeId, pharmacyType, freq, usageLevel, usage, dailyDosage } = formDetail;
        // 空中药房不同的 制法会对应不同的服用量
        const UsageInfoMap = ChineseMedicineUsageInfo.GetChineseUsageParam;
        const value = UsageInfoMap(medicineStateScopeId) || UsageInfoMap(usageScopeId);

        if (pharmacyType == PharmacyType.air && value) {
            const { defaultFreq, defaultUsageLevel } = value || {};
            formDetail.freq = defaultFreq ?? "";
            formDetail.usageLevel = defaultUsageLevel ?? "";
            const copyFormDetail = _.cloneDeep(formDetail);
            this._modifyChinesePrescriptionUsage(copyFormDetail);
            formDetail.usageDays = copyFormDetail.usageDays ?? "";
        } else {
            const chinesePRWithUsage = ChineseMedicineConfigProvider.getChinesePRWithUsage(event.value) || {};
            formDetail.dailyDosage = chinesePRWithUsage.dailyDosage ?? "";
            formDetail.freq = chinesePRWithUsage.freq ?? "";
            formDetail.usageLevel = chinesePRWithUsage.usageLevel ?? "";
            formDetail.usageDays = chinesePRWithUsage.usageDays ?? "";
        }
        if (pharmacyType == PharmacyType.air) {
            if (!this.setAirPharmacyUsage("usage", usage)) formDetail.usage = "";
            if (!this.setAirPharmacyUsage("usageLevel", usageLevel)) formDetail.usageLevel = "";
            if (!this.setAirPharmacyUsage("dosage", dailyDosage)) formDetail.dailyDosage = "";
            if (!this.setAirPharmacyUsage("freq", freq)) formDetail.freq = "";
        }
        if (this.isSpecialUsages()) {
            formDetail.dailyDosage = "";
        } else {
            formDetail.usageDays = "";
        }
        this.update();
    }

    @actionEvent(_EventSelectChinesePrescriptionUsageLevel)
    async *_mapEventSelectChinesePrescriptionUsageLevel(event: _EventSelectChinesePrescriptionUsageLevel): AsyncGenerator<State> {
        this.innerState.formDetail.usageLevel = event.value;
        this.computedAirPharmacyUSagDays();
        this.computedTisanesSack(false);
        this.update();
    }

    @actionEvent(_EventSelectChinesePrescriptionDailyDosage)
    async *_mapEventSelectChinesePrescriptionDailyDosage(event: _EventSelectChinesePrescriptionDailyDosage): AsyncGenerator<State> {
        this.innerState.formDetail.dailyDosage = event.value;
        this.computedTisanesSack(false);
        this.update();
    }

    @actionEvent(_EventSelectChinesePrescriptionFreq)
    async *_mapEventSelectChinesePrescriptionFreq(event: _EventSelectChinesePrescriptionFreq): AsyncGenerator<State> {
        this.innerState.formDetail.freq = event.value;
        this.computedAirPharmacyUSagDays();
        this.computedTisanesSack(false);
        this.update();
    }

    @actionEvent(_EventSelectChinesePrescriptionUsageUsageDays)
    async *_mapEventSelectChinesePrescriptionUsageUsageDays(event: _EventSelectChinesePrescriptionUsageUsageDays): AsyncGenerator<State> {
        this.innerState.formDetail.usageDays = event.value;
        this.update();
    }

    @actionEvent(_EventUpdateChineseMedicineAmount)
    async *_mapEventUpdateChineseMedicineAmount(event: _EventUpdateChineseMedicineAmount): AsyncGenerator<State> {
        const formDetail = this.innerState.formDetail as PrescriptionChineseForm;
        const { formItem, value } = event;
        const {  goodsInfo } = formItem || {};
        const { typeId, pieceUnit } = goodsInfo || {};
        const isMedicineChineseGranule = GoodsTypeId.medicineChineseGranule === typeId;
        // 中药颗粒 & 单位为g/克
        const isChineseGranuleAndUnitG = isMedicineChineseGranule && (pieceUnit === "g" || pieceUnit === "克");
        this.innerState.currentFocusItem = formItem.keyId;
        if (userCenter.enableEqCoefficient && formDetail.eqConversionRule === ChineseMedicineSpecType.chinesePiece && isChineseGranuleAndUnitG) {
            formItem.eqUnitCount = !!value ? Number(value) : undefined;
            formItem.unitCount = undefined;
        } else {
            formItem.unitCount = !!value ? Number(value) : undefined;
            formItem.eqUnitCount = undefined;
        }
        // 空中药房-代煎、颗粒，需要调用空中药房算费
        const pharmacyType = formDetail.vendor?.pharmacyType ?? formDetail.pharmacyType;
        const { medicineStateScopeId } = formDetail;
        if (pharmacyType == PharmacyType.air) {
            if (medicineStateScopeId == MedicineScopeId.daiJian || medicineStateScopeId == MedicineScopeId.keLi) {
                this._airPharmacyCalculateTrigger.next();
            }
        }
        this._clearAdjustmentFee(formItem);
        this.update();
    }

    @actionEvent(_EventSelectChineseMedicineBoilMethod)
    async *_mapEventSelectChineseMedicineBoilMethod(event: _EventSelectChineseMedicineBoilMethod): AsyncGenerator<State> {
        const { formItem } = event;
        const { specialRequirement } = ChineseMedicine;
        const initIndex = specialRequirement.findIndex((i) => i.name == formItem.specialRequirement);
        const selectIndex = await AbcDialog.showOptionsBottomSheet({
            title: "煎法",
            options: specialRequirement?.map((item) => item.name),
            initialSelectIndexes: initIndex > -1 ? new Set<number>([initIndex]) : undefined,
            crossAxisCount: 3,
            height: pxToDp(500),
        });
        if (!selectIndex || !selectIndex.length) return;
        formItem.specialRequirement = specialRequirement[selectIndex[0]].name;
        if (formItem.specialRequirement == "【自备】") {
            await Toast.show(`备注为【自备】，该药品将不会纳入划价收费`, { warning: true });
            formItem!.chargeType = DispensingFormItemsSourceItemType.noCharge;
        } else {
            formItem!.chargeType = DispensingFormItemsSourceItemType.default;
        }
        this.update();
    }

    @actionEvent(_EventAddChineseMedicineUsage)
    async *_mapEventAddChineseMedicineUsage(): AsyncGenerator<State> {
        const result = await showBottomPanel<{ process?: ChinesePrescriptionProcessingInfoRsp }>(
            <ChinesePrescriptionProcessView
                formDetail={this.innerState.formDetail as PrescriptionChineseForm}
                patient={this.innerState.outpatientSheetDetail?.patient}
            />,
            {
                topMaskHeight: pxToDp(200),
            }
        );
        if (!result) return;
        if (!!result?.process) {
            //用于中药处方详情卡片数据显示
            if (result.process.processName && result.process.processName?.indexOf("煎药") > -1) {
                const usageStr = `${!!result.process.processBagUnitCount ? "（1剂煎" + result.process.processBagUnitCount + "袋" : ""}${
                    !!result.process.totalProcessCount
                        ? "，共" + result.process.totalProcessCount + "袋）"
                        : !!result.process.processBagUnitCount
                        ? "）"
                        : ""
                }`;
                result.process.processName = `煎药${usageStr}`;
            } else if (!!result.process?.processRemark) {
                result.process.processName = result.process.processRemark;
            }
            Object.assign(this.innerState.formDetail, {
                ...result.process,
            });
            //删除药房---恢复成默认药房设置
            this.innerState.formDetail.prescriptionFormItems?.forEach((formItem) => {
                const transPharmacy = this.innerState.pharmacyInfoConfig?.getDefaultPharmacy({
                    departmentId: this.innerState.outpatientSheetDetail?.departmentId,
                    goodsInfo: { typeId: formItem.goodsInfo?.typeId },
                    processInfo: result.process,
                });
                this.innerState.selectPharmacyInfo = transPharmacy;
                if (!!transPharmacy && (formItem.pharmacyNo != transPharmacy.no || formItem.pharmacyType != transPharmacy.type)) {
                    formItem.pharmacyNo = transPharmacy.no;
                    formItem.pharmacyType = transPharmacy.type;
                    formItem.pharmacyName = transPharmacy.name ?? formItem.pharmacyName;
                }
            });

            //刷新药品库存
            GoodsAgent.searchChineseMedicineGoodsBySpec({
                cMSpec: ChineseMedicineSpecType.fullNames()[this.innerState.chineseMedicineSpecType!],
                list:
                    this.innerState.formDetail.prescriptionFormItems?.map((item) => ({
                        medicineCadn: item.displayName,
                        goodsId: item.id ?? "",
                        manufacturer: item.manufacturer,
                        keyId: item.keyId ?? UUIDGen.generate(),
                        cMSpec: item.cMSpec ?? ChineseMedicineSpecType.fullNames()[this.innerState.chineseMedicineSpecType!],
                        pieceUnit: item.pieceUnit ?? "",
                        extendSpec: item.goodsInfo?.extendSpec ?? "",
                    })) || [],
                pharmacyNo: this.innerState.selectPharmacyInfo?.no,
            }).then((rsp) => {
                this.innerState.formDetail.prescriptionFormItems = this.innerState.formDetail.prescriptionFormItems?.map((item) => {
                    const isExistGoods = rsp?.find((t) => t.keyId == item.keyId || t.keyId == item.goodsInfo.keyId);
                    const _newGoodInfo = !!isExistGoods?.goods?.id ? isExistGoods?.goods : undefined;
                    const sellUnits = _newGoodInfo?.sellUnits;
                    item.unit = !!sellUnits?.length ? ABCUtils.first(sellUnits) : undefined;
                    item.productInfo = _newGoodInfo;
                    return item;
                });
                this.update();
            });
        }
        this.update();
    }

    @actionEvent(_EventUpdateMedicineStateScopedId)
    async *_mapEventUpdateMedicineStateScopedId(): AsyncGenerator<State> {
        const formDetail = this.innerState.formDetail as PrescriptionChineseForm;
        const usageList = formDetail.vendor?.vendorAvailableMedicalStates;
        const options = usageList?.map((item) => item.medicalStatName);
        const initIndex = usageList?.findIndex((i) => i.medicalStatId == formDetail.medicineStateScopeId) ?? -1;
        const isAirPharmacy = formDetail.pharmacyType == PharmacyType.air;
        const chinesePrescriptionUsage = JsonMapper.deserialize(ChinesePrescriptionUsage, {
            freq: JsonMapper.deserialize(ChineseUsageItemInfo, {
                name: formDetail.freq,
            }),
            dailyDosage: JsonMapper.deserialize(ChineseUsageItemInfo, {
                name: formDetail.dailyDosage,
            }),
            usageDays: JsonMapper.deserialize(ChineseUsageItemInfo, {
                name: formDetail.usageDays,
            }),
            usageLevel: JsonMapper.deserialize(ChineseUsageItemInfo, {
                name: formDetail.usageLevel,
            }),
            usage: JsonMapper.deserialize(ChineseUsageItemInfo, {
                name: formDetail.usage,
            }),
            medicineStateScopeId: formDetail.medicineStateScopeId,
            usageScopeId: formDetail.usageScopeId,
            processBagUnitCount: formDetail.processBagUnitCount,
            totalProcessCount: formDetail.totalProcessCount,
            vendorInfo: formDetail.vendor,
            pharmacyType: formDetail.pharmacyType,
            processRemark: formDetail.processRemark,
            count: formDetail.doseCount,
        });
        if (
            formDetail.medicineStateScopeId == MedicineScopeId.keLi ||
            formDetail.medicineStateScopeId == MedicineScopeId.daiJian ||
            formDetail.medicineStateScopeId == MedicineScopeId.zhiJian
        ) {
            const info = await showBottomPanel<ChineseAirPharmacyBagsParam>(
                <ChineseAirPharmacyBagsDialog
                    options={options}
                    initIndex={initIndex}
                    chinesePrescriptionUsage={chinesePrescriptionUsage}
                    medicalStateList={usageList}
                />,
                {
                    topMaskHeight: pxToDp(160),
                }
            );
            if (_.isUndefined(info)) return;
            formDetail.medicineStateScopeId = usageList![info.selectIndex!].medicalStatId;
            //空中药房显示加工信息、代煎代配药房为饮片处方-代煎，也需要显示加工信息
            const isShowProcess =
                isAirPharmacy ||
                (formDetail.pharmacyType == PharmacyType.virtual && formDetail.medicineStateScopeId == MedicineScopeId.daiJian);
            formDetail.processBagUnitCount = isShowProcess ? info.processBagUnitCount : undefined;
            formDetail.totalProcessCount = isShowProcess ? info.totalProcessCount : undefined;
            formDetail.processRemark = isShowProcess ? info.processRemark : undefined;
        } else {
            const result = await AbcDialog.showOptionsBottomSheet({
                title: "药态",
                options: options,
                initialSelectIndexes: initIndex > -1 ? new Set<number>([initIndex]) : undefined,
            });
            if (!result || !result.length) return;
            formDetail.medicineStateScopeId = usageList![result[0]].medicalStatId;
        }

        if (this.innerState.chineseMedicineSpecType === ChineseMedicineSpecType.chinesePiece) {
            if (formDetail.pharmacyType == PharmacyType.air) {
                this._loadVendorList.next();
            } else if (formDetail.pharmacyType == PharmacyType.virtual && formDetail.medicineStateScopeId == MedicineScopeId.daiJian) {
                this._loadVirtualVendorList.next();
            }
        }
        if (
            isAirPharmacy &&
            (formDetail.medicineStateScopeId == MedicineScopeId.keLi || formDetail.medicineStateScopeId == MedicineScopeId.daiJian)
        ) {
            this._airPharmacyCalculateTrigger.next();
        }
        // 只要药态选择了【代煎】方式，那么isDecoction设为true，并且需要传1剂多少袋，共多少剂
        if (formDetail.medicineStateScopeId == MedicineScopeId.daiJian) {
            formDetail.isDecoction = true;
            formDetail.doseCount = formDetail.doseCount || 1;
            formDetail.processBagUnitCount = formDetail.processBagUnitCount || 3;
            formDetail.totalProcessCount = formDetail.totalProcessCount || (formDetail.doseCount || 1) * formDetail.processBagUnitCount;
        }
        this.update();
    }

    @actionEvent(_EventSelectAirExpress)
    async *_mapEventSelectAirExpress(event: _EventSelectAirExpress): AsyncGenerator<State> {
        const { outpatientSheetDetail } = this.innerState,
            formDetail = this.innerState.formDetail as PrescriptionChineseForm;
        let result;
        const otherAirPharmacyCalculateForm = this.combinedAirPharmacyPrescription(
            outpatientSheetDetail?.prescriptionChineseForms?.filter((t) => t.isAirPharmacy)
        );
        const chargeForm = new ChargeForm();
        chargeForm.keyId = UUIDGen.generate();
        chargeForm.sourceFormType = ChargeSourceFormType.airPharmacy;
        chargeForm.status = ChargeStatus.unCharged;

        const {
            freq,
            usageLevel,
            specification,
            dailyDosage,
            usage,
            usageDays,
            doseCount,
            vendorName,
            vendorId,
            pharmacyNo,
            pharmacyType,
            pharmacyName,
            usageScopeId,
            medicineStateScopeId,
            vendor,
            deliveryInfo,
            prescriptionFormItems,
        } = formDetail;
        chargeForm.usageInfo = chargeForm.usageInfo ?? new UsageInfo();
        chargeForm.vendorName = vendorName;
        chargeForm.vendorId = vendorId;
        chargeForm.medicineStateScopeId = medicineStateScopeId;
        chargeForm.medicineStateScopeName = vendor?.vendorAvailableMedicalStates?.find(
            (item) => item.medicalStatId == medicineStateScopeId
        )?.medicalStatName;
        chargeForm.pharmacyType = vendor?.pharmacyType ?? pharmacyType;
        chargeForm.pharmacyName = vendor?.pharmacyName;
        chargeForm.pharmacyNo = vendor?.pharmacyNo;
        chargeForm.usageScopeId = usageScopeId as UsageScopeId;
        chargeForm.deliveryInfo = JsonMapper.deserialize(DeliveryInfo, deliveryInfo);
        chargeForm.chargeFormItems = !_.isEmpty(chargeForm.chargeFormItems) ? chargeForm.chargeFormItems : [];
        Object.assign(chargeForm.usageInfo, {
            freq,
            usageLevel,
            specification,
            dailyDosage,
            usage,
            usageDays,
            doseCount,
            vendorName,
            vendorId,
            medicineStateScopeId,
            medicineStateScopeName: vendor?.vendorAvailableMedicalStates?.find((item) => item.medicalStatId == medicineStateScopeId)
                ?.medicalStatName,
            pharmacyNo,
            pharmacyType,
            pharmacyName,
            usageScopeId,
            deliveryInfo,
            prescriptionFormItems,
        });
        chargeForm.fixAirPharmacyPrescriptionInfo();
        if (event.pharmacyType == PharmacyType.virtual) {
            result = await DeliveryInfoEditPage.show({
                patient: outpatientSheetDetail?.patient ?? new Patient(),
                deliveryInfo: chargeForm.deliveryInfo ?? new DeliveryInfo(),
                payTypeMutable: true,
                addressMutable: true,
                deliverFeeEnable: false,
                chargeForm: chargeForm,
            });
        } else {
            result = await AirPharmacyDeliveryInfoEditPage.show({
                patient: outpatientSheetDetail?.patient ?? new Patient(),
                chargeForm: chargeForm,
                payTypeMutable: true,
                addressMutable: true,
                deliverFeeEnable: true,
                otherAirPharmacyCalculateForm: otherAirPharmacyCalculateForm,
                airPharmacySort: outpatientSheetDetail?.prescriptionChineseForms?.length ?? 0,
            });
        }

        if (result && result.deliverInfo) {
            formDetail.deliveryInfo = JsonMapper.deserialize(PrescriptionFormDelivery, {
                ...result?.deliverInfo,
                deliveryCompanyId: result.deliverInfo?.deliveryCompany?.id,
                deliveryFee: result.deliverInfo?.expectDeliverFee__,
            });
            if (
                event.pharmacyType == PharmacyType.air &&
                (medicineStateScopeId == MedicineScopeId.keLi || medicineStateScopeId == MedicineScopeId.daiJian)
            ) {
                this._airPharmacyCalculateTrigger.next();
            }
        }
        this.update();
    }

    @actionEvent(_EventUpdateChinesePrescriptionRequirement)
    async *_mapEventUpdateChinesePrescriptionRequirement(): AsyncGenerator<State> {
        const { requirement } = this.innerState.formDetail as PrescriptionChineseForm;
        const requirementList = ChineseMedicine.requirement;
        const initIndex = requirementList.findIndex((i) => i.name == requirement);
        const selectIndex = await AbcDialog.showOptionsBottomSheet({
            title: "备注",
            options: [...requirementList, { name: "其他" }]?.map((item) => item.name),
            initialSelectIndexes: initIndex > -1 ? new Set<number>([initIndex]) : undefined,
            crossAxisCount: 4,
            height: pxToDp(500),
        });
        if (!selectIndex?.length) return;
        if (typeof selectIndex == "string" && selectIndex == "其他") {
            const _value = await MedicineUsagePicker.showCustomUsageDialog({
                title: "备注",
                value: requirement,
                enableDefaultToolBar: false,
                maxLength: 200,
            });
            if (_value != undefined) {
                this.innerState.formDetail.requirement = _value?.name;
            }
        } else {
            this.innerState.formDetail.requirement = requirementList[selectIndex[0]].name;
        }
        this.update();
    }

    @actionEvent(_EventInitEqConversionRule)
    async *_mapEventInitEqConversionRule(event: _EventInitEqConversionRule): AsyncGenerator<State> {
        this.innerState.formDetail.eqConversionRule = event.value;
        this.update();
    }

    @actionEvent(_EventSelectChineseGranuleOptions)
    async *_mapEventSelectChineseGranuleOptions(): AsyncGenerator<State> {
        const { eqConversionRule } = this.innerState.formDetail;
        let initIndex = eqConversionRule;
        initIndex = initIndex ?? ChineseMedicineSpecType.chinesePiece;
        const result = await showOptionsBottomSheet({
            title: "",
            options: conversionTypeList.map((item) => item.label),
            initialSelectIndexes: new Set<number>([initIndex]),
            height: pxToDp(164),
            showCloseButton: false,
            needTitleBar: false,
        });
        if (result && result.length) {
            const oldEqConversionRule = this.innerState.formDetail.eqConversionRule;
            this.innerState.formDetail.eqConversionRule = conversionTypeList[result[0]].value;
            const { eqConversionRule, prescriptionFormItems } = this.innerState.formDetail;
            // 切换类型要更改字段数据绑定
            if (oldEqConversionRule !== eqConversionRule) {
                prescriptionFormItems?.forEach((formItem) => {
                    const { eqCoefficient, typeId, pieceUnit } = formItem.goodsInfo;
                    const isMedicineChineseGranule = GoodsTypeId.medicineChineseGranule === typeId;

                    // 中药颗粒 & 单位为g/克
                    const isChineseGranuleAndUnitG = isMedicineChineseGranule && (pieceUnit === "g" || pieceUnit === "克");
                    if (eqConversionRule === ChineseMedicineSpecType.chinesePiece) {
                        if (isChineseGranuleAndUnitG) {
                            formItem.eqUnitCount = !!formItem.unitCount ? Number(formItem.unitCount) : undefined;
                            formItem.eqCoefficient = eqCoefficient;
                            formItem.unitCount = undefined;
                        } else {
                            formItem.unitCount = !!formItem.unitCount ? Number(formItem.unitCount) : undefined;
                            formItem.eqUnitCount = undefined;
                            formItem.eqCoefficient = undefined;
                        }
                    } else {
                        if (isChineseGranuleAndUnitG) {
                            formItem.unitCount = !!formItem.eqUnitCount ? Number(formItem.eqUnitCount) : undefined;
                        } else {
                            formItem.unitCount = !!formItem.unitCount ? Number(formItem.unitCount) : undefined;
                        }
                        formItem.eqUnitCount = undefined;
                        formItem.eqCoefficient = undefined;
                    }
                });
            }
            this._calculatePriceTrigger.next();
            this.update();
        }
    }

    @actionEvent(_EventChineseMedicineSpecType)
    async *_mapEventChineseMedicineSpecType(event: _EventChineseMedicineSpecType): AsyncGenerator<State> {
        await this._trans(event);

        if (this.innerState.keyword && this.innerState.keyword !== "") {
            this._searchGoodsInfoTrigger.next(0);
        }
        this._searchInput?.focus();
        this.update();
    }

    @actionEvent(_EventBackPage)
    async *_mapEventBackPage(): AsyncGenerator<State> {
        //中药处方返回之前，需要匹配当前选中药房信息（selectPharmacyInfo在使用中只针对本地多药房，而代煎代配、空中药房没有用到，此时需要回显，否则一直都是本地药房）
        //解决：诊选择处方模板后，修改为代煎代配药房，收费完成后在药房发药时提示“不同药房类型发药
        if (this.innerState.isChinesePrescription) {
            const formDetail = this.innerState.formDetail as PrescriptionChineseForm;
            if (
                !formDetail.pharmacyType &&
                formDetail?.pharmacyType != this.innerState.selectPharmacyInfo?.type &&
                formDetail?.pharmacyNo != this.innerState.selectPharmacyInfo?.no
            ) {
                this.innerState.selectPharmacyInfo = this.innerState.pharmacyInfoConfig?.pharmacyList?.find(
                    (t) => t.type == formDetail?.pharmacyType && t.no == formDetail?.pharmacyNo
                );
            }
        }
        if (this.innerState.hasChanged) {
            ABCNavigator.pop({
                formData: this.formatSubmitFormDetail(this.innerState.formDetail),
                extraParameter: {
                    patient: this.innerState.outpatientSheetDetail?.patient,
                    psychotropicNarcoticEmployee: this.innerState.outpatientSheetDetail?.psychotropicNarcoticEmployee,
                },
            });
        } else {
            ABCNavigator.pop();
        }
    }

    /**
     * 对于没有productInfo的药品，需要清空goodsId(解决接诊时提示中药饮片与颗粒不可混开问题)
     * @param formDetail
     * @private
     */
    private _clearGoodsIdForNoProductInfo(formDetail: BasePrescriptionForm): void {
        formDetail.prescriptionFormItems?.forEach((formItem) => {
            if (!formItem.productInfo) {
                formItem.goodsId = undefined;
            }
        });
    }

    private formatSubmitFormDetail(formDetail: BasePrescriptionForm) {
        formDetail.prescriptionFormItems?.forEach((formItem) => {
            formItem.groupId = formItem.groupId == ChargeUtils.maxGroupId ? undefined : formItem.groupId;
        });
        // 清除议价信息
        this._clearAdjustmentFeeFormDetail(formDetail);
        this._clearGoodsIdForNoProductInfo(formDetail);
        if (this.innerState.isChinesePrescription) {
            formDetail.specification = formDetail.specification || "中药饮片";
        }
        return formDetail;
    }

    // 复制模板
    requestCopyTemplate(type?: MedicineAddType): void {
        this.dispatch(new _EventCopyTemplate(type));
    }

    requestSaveTemplate(): void {
        this.dispatch(new _EventSaveTemplate());
    }

    requestChangeArrangement(): void {
        this.dispatch(new _EventChangeArrangement());
    }

    // 添加组
    requestAddMedicineGroup(): void {
        this.dispatch(new _EventAddMedicineGroup());
    }

    // 修改药房类型
    requestNewModifyPharmacyType(): void {
        this.dispatch(new _EventModifyPharmacyType());
    }

    //添加药品
    requestAddMedicinePrescription(goodsInfo: GoodsInfo, group?: number): void {
        this.dispatch(new _EventAddMedicinePrescription(goodsInfo, group));
    }

    //保存页面中的输入框组件
    requestCacheSearchInput(element?: AbcTextInput | null): void {
        if (!element) return;
        this.dispatch(new _EventCacheSearchInput(element));
    }

    requestRecordPartLayout(key: string, layoutY: number): void {
        this.dispatch(new _EventRecordPartLayout(key, layoutY));
    }

    //点击确认处方
    requestSubmit(): void {
        this.dispatch(new _EventSubmit());
    }

    requestUpdateWesternMedicineGroupIndex(formItem: PrescriptionFormItem): void {
        this.dispatch(new _EventUpdateWesternMedicineGroupIndex(formItem));
    }

    /**
     * 重置皮试状态
     * @param formItem
     */
    requestResetWesternAst(formItem: PrescriptionFormItem): void {
        this.dispatch(new _EventResetWesternAstt(formItem));
    }

    /**
     * 取消分组
     *（删除存在的分组，恢复成默认状态）
     */
    requestDeleteWesternMedicineGroupIndex(): void {
        this.dispatch(new _EventDeleteWesternMedicineGroupIndex());
    }

    /**
     * 修改分组
     * @param status
     */
    requestModifyEditGroupState(status: boolean): void {
        this.dispatch(new _EventModifyEditGroupState(status));
    }

    /**
     * 修改处方类型
     */
    requestSelectPrescriptionType(): void {
        this.dispatch(new _EventModifyPrescriptionType());
    }

    //修改精麻类型
    requestModifyPrescriptionNarcoticType(type?: number): void {
        this.dispatch(new _EventModifyPrescriptionNarcoticType(type));
    }

    /**
     * 删除备注
     * @param formItem
     */
    requestDeleteWesternRemark(formItem: PrescriptionFormItem): void {
        this.dispatch(new _EventDeleteWesternRemark(formItem));
    }

    /**
     * 删除
     * @param formItem
     */
    requestDeleteMedicine(formItem: PrescriptionFormItem): void {
        this.dispatch(new _EventDeleteMedicine(formItem));
    }

    /**
     * 更新西药处方的用法
     * @param formItem
     * @param usage
     */
    requestUpdateWesternMedicineUsage(formItem: PrescriptionFormItem, usage?: string): void {
        this.dispatch(new _EventUpdateWesternMedicineUsage(formItem, usage));
    }

    /**
     * 更新西药处方的频率
     * @param formItem
     * @param freq
     */
    requestUpdateWesternMedicineFreq(formItem: PrescriptionFormItem, freq?: string): void {
        this.dispatch(new _EventUpdateWesternMedicineFreq(formItem, freq));
    }

    /**
     * //更改西药处方的频率剂量
     * @param formItem
     * @param dosageCount
     */
    requestUpdateWesternDosageCount(formItem: PrescriptionFormItem, dosageCount: string): void {
        this.dispatch(new _EventUpdateWesternDosageCount(formItem, dosageCount));
    }

    /**
     * 更新西药处方频率剂量单位
     */
    requestUpdateDosageUnit(formItem: PrescriptionFormItem, unit: string): void {
        this.dispatch(new _EventUpdateDosageUnit(formItem, unit));
    }

    /**
     * 修改西药处方的天数
     * @param formItem
     * @param days
     */
    requestUpdateWesternMedicineDays(formItem: PrescriptionFormItem, days: string): void {
        this.dispatch(new _EventUpdateWesternMedicineDays(formItem, days));
    }

    /**
     * 更新西药的总量
     * @param formItem
     * @param unitCount
     */
    requestUpdateWesternAmountUnitCount(formItem: PrescriptionFormItem, unitCount: string): void {
        this.dispatch(new _EventUpdateWesternAmountUnitCount(formItem, unitCount));
    }

    /**
     * 更新西药的总用量的单位
     * @param formItem
     * @param unit
     */
    requestUpdateWesternAmountUnit(formItem: PrescriptionFormItem, unit: string): void {
        this.dispatch(new _EventUpdateWesternAmountUnit(formItem, unit));
    }

    /**
     * 搜索状态调整
     * @param focus
     */
    requestUpdateSearchbarStatus(focus: boolean): void {
        this.dispatch(new _EventContinueSearch(focus));
    }

    /**
     * 单项议折扣
     * @param formItem
     * @param discount
     * @param type
     */
    requestAdjustDiscount(formItem: PrescriptionFormItem, discount: number, type: string): void {
        this.dispatch(new _EventAdjustDiscount(formItem, discount, type));
    }

    /**
     * 单项议总价
     * @param formItem
     * @param totalPrice
     * @param type
     */
    requestAdjustTotalPrice(formItem: PrescriptionFormItem, totalPrice: number, type: string): void {
        this.dispatch(new _EventAdjustTotalPrice(formItem, totalPrice, type));
    }

    /**
     * 查看药品批次信息
     */
    requestCheckGoodsBatchInfo(): void {
        this.dispatch(new _EventCheckGoodsBatchInfo());
    }

    /**
     * 修改西药处方皮试、续用、免试等注射方法
     * @param formItem
     * @param ast
     */
    requestUpdateWesternMedicineAst(formItem: PrescriptionFormItem, ast: number): void {
        this.dispatch(new _EventUpdateWesternMedicineAst(formItem, ast));
    }

    /**
     * 修改药品来源药房
     * @param formItem
     * @param pharmacy
     */
    requestModifyMedicinePharmacy(formItem: PrescriptionFormItem, pharmacy: PharmacyListConfig): void {
        this.dispatch(new _EventModifyMedicinePharmacy(formItem, pharmacy, undefined));
    }

    /**
     * 修改药品自备状态
     * @param formItem
     * @param status
     */
    requestModifyMedicineSelfProvidedStatus(formItem: PrescriptionFormItem, status: DispensingFormItemsSourceItemType): void {
        this.dispatch(new _EventModifyMedicineSelfProvidedStatus(formItem, status));
    }

    /**
     * 更新西药的备注
     * @param formItem
     * @param remark
     */
    requestUpdateWesternRemark(formItem: PrescriptionFormItem, remark: string): void {
        this.dispatch(new _EventUpdateWesternRemark(formItem, remark));
    }

    /**
     * 更新输注处方滴速
     */
    requestUpdateInfusionPrescriptionIvgtt(groupId: number, ivgtt: number): void {
        this.dispatch(new _EventUpdateInfusionPrescriptionIvgtt(groupId, ivgtt));
    }

    //删除组
    requestDeleteGroup(group: number): void {
        this.dispatch(new _EventDeleteGroup(group));
    }

    /**
     * 搜索商品
     * @param keyword
     */
    requestGoodsList(keyword: string): void {
        this.dispatch(new _EventSearchGoods(keyword));
    }

    /**
     * 中药处方-搜索切换饮片/颗粒
     */
    requestChineseSwitchType(): void {
        this.dispatch(new _EventChineseSwitchType());
    }

    /**
     * 中药处方总剂量
     * @param dosageCount
     */
    requestUpdateChinesePrescriptionDosageAmount(dosageCount: string): void {
        this.dispatch(new _EventUpdateChinesePrescriptionDosageAmount(dosageCount));
    }

    /**
     * 更新中药处方的用法（煎服）
     * @param usage
     */
    requestSelectChinesePrescriptionUsage(usage: string): void {
        this.dispatch(new _EventSelectChinesePrescriptionUsage(usage));
    }

    /**
     * 更改中药处方每日剂量
     * @param dailyDosage
     */
    requestSelectChinesePrescriptionDailyDosage(dailyDosage: string): void {
        this.dispatch(new _EventSelectChinesePrescriptionDailyDosage(dailyDosage));
    }

    /**
     * 更新中药处方频率
     * @param freq
     */
    requestSelectChinesePrescriptionFreq(freq: string): void {
        this.dispatch(new _EventSelectChinesePrescriptionFreq(freq));
    }

    //更新中药处方的使用级别"每次200ml"
    requestSelectChinesePrescriptionUsageLevel(usageLevel: string): void {
        this.dispatch(new _EventSelectChinesePrescriptionUsageLevel(usageLevel));
    }

    requestSelectChinesePrescriptionUsageUsageDays(usageDays: string): void {
        this.dispatch(new _EventSelectChinesePrescriptionUsageUsageDays(usageDays));
    }

    /**
     * 更新单个中药的数量
     * @param formItem
     * @param unitCount
     */
    requestUpdateChineseMedicineAmount(formItem: PrescriptionFormItem, unitCount: string): void {
        this.dispatch(new _EventUpdateChineseMedicineAmount(formItem, unitCount));
    }

    /**
     * 修改中药煎法
     * @param formItem
     */
    requestSelectChineseMedicineBoilMethod(formItem: PrescriptionFormItem): void {
        this.dispatch(new _EventSelectChineseMedicineBoilMethod(formItem));
    }

    /**
     * 中药处方--用法
     */
    requestAddChineseMedicineUsage(tabType?: MedicineChinesePanelType): void {
        this.dispatch(new _EventAddChineseMedicineUsage(tabType));
    }

    /**
     * 更新空中药房--药态选中信息
     */
    requestUpdateMedicineStateScopeId(): void {
        this.dispatch(new _EventUpdateMedicineStateScopedId());
    }

    /**
     * 空中药房--快递选择
     * @param pharmacyType
     */
    requestSelectAirExpress(pharmacyType: PharmacyType): void {
        this.dispatch(new _EventSelectAirExpress(pharmacyType));
    }

    /**
     * 更新中药处方备注信息
     */
    requestUpdateChinesePrescriptionRequirement(): void {
        this.dispatch(new _EventUpdateChinesePrescriptionRequirement());
    }

    /**
     * 初始化按实际颗粒量开方
     */
    requestInitEqConversionRule(value: number): void {
        this.dispatch(new _EventInitEqConversionRule(value));
    }
    /**
     * 选择按等效饮片量开方/按实际颗粒量开方
     */
    requestSelectChineseGranuleOptions(): void {
        this.dispatch(new _EventSelectChineseGranuleOptions());
    }

    /**
     * 改变中药的搜索类型
     * @param type
     * @param forceTrans
     * @param pharmacyType
     */
    requestChineseMedicineSpecType(type: number, forceTrans?: boolean, pharmacyType?: number): void {
        if (_.isUndefined(type)) return;
        this.dispatch(new _EventChineseMedicineSpecType(type, forceTrans, pharmacyType));
    }

    /**
     * 手动返回时触发
     */
    requestBackPage(): void {
        this.dispatch(new _EventBackPage());
    }

    private async _initPageConfig(): Promise<void> {
        this.innerState.arrangement = !sharedPreferences.getInt(CHINESE_ARRANGEMENT);
        this.innerState.pharmacyInfoConfig = await userCenter.getInventoryChainConfig(false);
        this.innerState.westernMedicineConfig = WesternMedicineConfigProvider.getConfig();
        this.innerState.chineseMedicineConfig = ChineseMedicineConfigProvider.getConfig();

        const isChinesePrescription = this.innerState.isChinesePrescription;
        const [employeesMeConfig, dispensingConfig, usageConfig, airPharmacyConfig, virtualPharmacyConfig] = await Promise.all([
            ClinicAgent.getEmployeesMeConfig().catchIgnore(),
            OnlinePropertyConfigProvider.instance.getClinicDispensingConfig().catchIgnore(), //读取当前加工配置
            OutpatientAgent.getUsageConfig(false),
            isChinesePrescription ? ClinicAgent.getAirPharmacyConfig(true).catchIgnore() : undefined,
            isChinesePrescription ? ClinicAgent.getVirtualPharmacyConfig(true).catchIgnore() : undefined,
        ]);
        this.innerState.employeesMeConfig = employeesMeConfig;
        this.innerState.dispensingConfig = dispensingConfig;
        //拉取配置，防止第一次算计量失败
        this.innerState.chinesePrescriptionSupportMix = usageConfig.chinesePrescriptionSupportMix;
        this.innerState.airPharmacyConfig = airPharmacyConfig;
        this.innerState.virtualPharmacyConfig = virtualPharmacyConfig;
    }

    private _initPageTrigger(): void {
        this._searchGoodsInfoTrigger
            .pipe(
                switchMap(() => {
                    this.innerState.loadError = null;
                    if (!!this.innerState.keyword) {
                        this.innerState.loading = true;
                        this.update();
                    }
                    if (this.innerState.isChinesePrescription) {
                        const { searchChineseSpecType = 0, chineseMedicineSpecType, outpatientSheetDetail } = this.innerState;
                        const formDetail = this.innerState.formDetail as PrescriptionChineseForm,
                            { sex, age } = outpatientSheetDetail?.patient || {},
                            { chiefComplaint = "", diagnosis = "" } = outpatientSheetDetail?.medicalRecord || {},
                            { pharmacyType, vendor, pharmacyNo } = formDetail;
                        if (pharmacyType == PharmacyType.air) {
                            if (this.innerState.keyword == "") {
                                return of(null);
                            }
                            return GoodsAgent.searchAirPharmacyMedicines(
                                this.innerState.keyword ?? "",
                                searchChineseSpecType ?? chineseMedicineSpecType
                                    ? ChineseGoodType.chineseGranule
                                    : ChineseGoodType.chinesePiece,
                                vendor?.vendorId ?? "",
                                1,
                                vendor?.chooseMedicalStateId ?? "",
                                vendor?.vendorUsageScopeId ?? ""
                            )
                                .catch((error) => new ABCError(error))
                                .toObservable();
                        }

                        return GoodsAgent.smartSearchMedicine({
                            keyword: this.innerState.keyword,
                            clinicId: this._searchClinicId,
                            spec: ChineseMedicineSpecType.fullNames()[searchChineseSpecType],
                            chiefComplaint: chiefComplaint,
                            diagnosis: diagnosis,
                            jsonType: [{ type: GoodsType.medicine, subType: [GoodsSubType.medicineChinese] }],
                            sex: sex,
                            age: age,
                            goodsIds: [],
                            offset: 0,
                            limit: 50,
                            withDomainMedicine: 1,
                            pharmacyType: this.innerState.formDetail.pharmacyType?.toString(),
                            pharmacyNo:
                                (this.innerState.pharmacyInfoConfig?.isOpenMultiplePharmacy &&
                                this.innerState.pharmacy == PharmacyType.normal
                                    ? this.innerState.selectPharmacyInfo?.no?.toString()
                                    : pharmacyNo?.toString()) ?? "",
                            searchByRoot: 0,
                            needAlias: 1,
                        })
                            .catch((error) => new ABCError(error))
                            .toObservable();
                    }
                    return GoodsAgent.searchWesternMedicine(this.innerState.keyword ?? "", this._searchClinicId)
                        .catch((error) => new ABCError(error))
                        .toObservable();
                })
            )
            .subscribe((data) => {
                this.innerState.loading = false;
                if (data == null) {
                    this.update();
                    return;
                }
                if (data instanceof ABCError) {
                    this.innerState.loadError = data.detailError;
                    this.update();
                    return;
                }

                const _inStock: GoodsInfo[] = [],
                    _noStock: GoodsInfo[] = [];
                data?.forEach((item: GoodsInfo) => {
                    if (item.inStock) _inStock.push(item);
                    else _noStock.push(item);
                });
                this.innerState.searchInfo = _inStock;
                this.innerState.searchListNoStock = _noStock;
                this.update();
            })
            .addToDisposableBag(this);
        /**
         * 加载供应商列表(空中药房)
         */
        this._loadVendorList
            .pipe(
                switchMap(() => {
                    const formDetail = this.innerState.formDetail as PrescriptionChineseForm,
                        { prescriptionFormItems } = formDetail;
                    if (!prescriptionFormItems?.length) return of(undefined);
                    const { doseCount = 1, medicineStateScopeId, usageScopeId, vendorId, pharmacyNo, specification } = formDetail;
                    return ChargeAgent.getVendors({
                        airPharmacyFormItems: prescriptionFormItems,
                        doseCount,
                        goodsTypeId: ChineseMedicineSpecType.typeFromName(specification)
                            ? ChineseGoodType.chineseGranule
                            : ChineseGoodType.chinesePiece,
                        medicineStateScopeId,
                        usageScopeId,
                        vendorId,
                        pharmacyNo,
                    })
                        .catchIgnore()
                        .toObservable();
                })
            )
            .subscribe((rsp) => {
                if (!rsp) return;
                const formDetail = this.innerState.formDetail as PrescriptionChineseForm,
                    { prescriptionFormItems } = formDetail;
                const currentVendor = formDetail.vendor;
                const selectVendor = rsp?.find((item) =>
                    !!currentVendor?.vendorUsageScopeId
                        ? item?.vendorUsageScopeId == currentVendor?.vendorUsageScopeId
                        : item.vendorId == currentVendor?.vendorId && item.vendorName == currentVendor?.vendorName
                );
                if (!!selectVendor) {
                    //解决后台没有返回orderItems的问题
                    const updateOrderItems: AirPharmacyOrderItem[] = [];
                    prescriptionFormItems?.forEach((formItem) => {
                        const goodsInfo = formItem.goodsInfo;
                        const isExist = selectVendor?.orderItems?.filter((t) => t?.displayName == goodsInfo.displayName);
                        if (!!isExist?.length) {
                            isExist.forEach((t) => {
                                updateOrderItems.push(t);
                            });
                        } else {
                            updateOrderItems.push(
                                JsonMapper.deserialize(AirPharmacyOrderItem, {
                                    ...goodsInfo,
                                    productInfo: undefined,
                                    unitPrice: goodsInfo.unitPriceWithUnit(formItem.unit ?? "g"),
                                })
                            );
                        }
                    });
                    selectVendor.orderItems = updateOrderItems?.map((item) => {
                        item.unit = item.unit ?? "g";
                        return item;
                    });
                    formDetail.vendor = selectVendor;
                    formDetail.vendorId = selectVendor.vendorId;
                    formDetail.vendorUsageScopeId = selectVendor.vendorUsageScopeId;
                    formDetail.vendorName = selectVendor.vendorName;
                    //TODO 赋值代煎方式

                    for (const medicine of selectVendor.orderItems ?? []) {
                        const _sameFormItems =
                            prescriptionFormItems?.find((item) =>
                                !!medicine.productId && !!item.goodsId
                                    ? item.goodsId == medicine.productId && item.goodsInfo.displayName == medicine?.displayName
                                    : item.goodsInfo.displayName == medicine?.displayName
                            ) ??
                            JsonMapper.deserialize(PrescriptionFormItem, {
                                keyId: UUIDGen.generate(),
                            });
                        //精准匹配到才直接替换
                        if (!!medicine?.productInfo?.isFindOneToReplace) {
                            OutpatientUtils.fillPrescriptionFromItem(
                                _sameFormItems,
                                medicine.productInfo!,
                                JsonMapper.deserialize(MedicineUsage, {
                                    unit: medicine?.unit,
                                    unitCount: _sameFormItems.unitCount ?? 1,
                                })
                            );
                        } else {
                            _sameFormItems.unit = medicine?.unit;
                            _sameFormItems.unitCount = _sameFormItems?.unitCount ?? 1;
                            _sameFormItems.name = medicine.displayName;
                            _sameFormItems.unitPrice = medicine?.unitPrice;
                        }
                    }
                    //空中药房,需要匹配上次记忆用法用量是否存在当前供应商配置中，不存在，需要置空
                    if (formDetail.pharmacyType == PharmacyType.air && formDetail.usageScopeId) {
                        const { usageOptions, freqOptions, dosageOptions, usageLevelOptions } = selectVendor?.businessScopeConfig ?? {};
                        if (!usageOptions?.find((t) => t?.name == formDetail.usage)) {
                            formDetail.usage = "";
                        }
                        if (!!freqOptions?.length && !freqOptions?.find((t) => t?.name == formDetail.freq)) {
                            formDetail.freq = "";
                        }
                        if (!!dosageOptions?.length && !dosageOptions?.find((t) => t?.name == formDetail.dailyDosage)) {
                            formDetail.dailyDosage = "";
                        }
                        if (!!usageLevelOptions?.length && !usageLevelOptions?.find((t) => t?.name == formDetail.usageLevel)) {
                            formDetail.usageLevel = "";
                        }
                    }
                    this.computedAirPharmacyUSagDays();
                    this.update();
                }
            })
            .addToDisposableBag(this);
        /**
         * 加载虚拟药房列表
         */
        this._loadVirtualVendorList
            .pipe(
                switchMap(() => {
                    const formDetail = this.innerState.formDetail as PrescriptionChineseForm,
                        { prescriptionFormItems } = formDetail;
                    if (!prescriptionFormItems?.length) return of(undefined);
                    return ChargeAgent.getVirtualVendorList().catchIgnore().toObservable();
                })
            )
            .subscribe((rsp) => {
                if (!rsp) return;
                const formDetail = this.innerState.formDetail as PrescriptionChineseForm,
                    { medicineStateScopeId, pharmacyName } = formDetail;
                let virtualList: AirPharmacyVendor[] = [];
                if (this.innerState.chineseMedicineSpecType == ChineseMedicineSpecType.chineseGranule) {
                    virtualList = rsp?.supportCmParticlesVendors ?? [];
                } else if (this.innerState.chineseMedicineSpecType == ChineseMedicineSpecType.chinesePiece) {
                    virtualList = rsp?.supportCmSlicesVendors ?? [];
                }
                //虚拟药房--饮片和颗粒供应商的venderId有相同的，所以如果饮片和颗粒相互切换时，会选中对应vendorId相同的一项
                const vendorId = formDetail.vendorId;
                const selectVendor =
                    virtualList.find((item) => {
                        const verifyRule1 = !!vendorId
                            ? item.vendorId == vendorId
                            : !!pharmacyName
                            ? item.vendorName == pharmacyName
                            : false;
                        const verifyRule2 = !!item.vendorAvailableMedicalStates?.find((t) => t.medicalStatId == medicineStateScopeId);
                        return verifyRule1 && (!medicineStateScopeId || verifyRule2);
                    }) ?? virtualList[0];
                if (selectVendor) {
                    Object.assign(formDetail, {
                        ...selectVendor,
                    });
                }
            })
            .addToDisposableBag(this);
        this._calculatePriceTrigger
            .pipe(
                debounceTime(300),
                switchMap(() => {
                    return fromPromise(
                        this._calculatePrice()
                            .then((rsp) => {
                                return { calculateRspData: rsp };
                            })
                            .catch((e) => new ABCError(e))
                    );
                })
            )
            .subscribe((rsp) => {
                if (rsp instanceof ABCError) {
                    this.dispatch(
                        new _EventUpdateCalculatePrice({
                            calculating: false,
                            calculateRspData: null,
                            error: rsp,
                            shouldNotCopyExpectedPrice: false,
                        })
                    );
                } else {
                    const { calculateRspData } = rsp;
                    this.dispatch(
                        new _EventUpdateCalculatePrice({
                            calculating: false,
                            calculateRspData: calculateRspData,
                            error: null,
                            shouldNotCopyExpectedPrice: false,
                        })
                    );
                }
            })
            .addToDisposableBag(this);
        this._airPharmacyCalculateTrigger
            .pipe(
                switchMap(() => {
                    const formDetail = this.innerState.formDetail as PrescriptionChineseForm;
                    const isPiece = this.innerState.chineseMedicineSpecType == ChineseMedicineSpecType.chinesePiece;
                    const params = new AirPharmacyCalculateReq();
                    const {
                        deliveryInfo,
                        doseCount,
                        medicineStateScopeId,
                        usageScopeId,
                        vendorId,
                        usage,
                        dailyDosage,
                        freq,
                        usageLevel,
                        totalProcessCount,
                        prescriptionFormItems,
                    } = formDetail;
                    if (!prescriptionFormItems?.length) return of(null);
                    const productItems = prescriptionFormItems?.map((formItem) => {
                        return {
                            productId: formItem.goodsId,
                            name: formItem.displayName,
                            unitPrice: formItem.unitPrice,
                            unit: formItem?.unit,
                            unitCount: formItem?.unitCount,
                            doseCount,
                        };
                    });
                    params.forms = [
                        {
                            deliveryInfo,
                            deliveryPrimaryFormId: "",
                            goodsTypeId: isPiece ? ChineseGoodType.chinesePiece : ChineseGoodType.chineseGranule,
                            items: productItems ?? [],
                            medicineStateScopeId: medicineStateScopeId,
                            usageInfo: {
                                dailyDosage,
                                doseCount,
                                freq: freq,
                                totalProcessCount: medicineStateScopeId == MedicineScopeId.daiJian ? totalProcessCount : undefined,
                                usage: usage,
                                usageLevel: usageLevel,
                            },
                            usageScopeId: usageScopeId,
                            vendorId: vendorId,
                            keyId: formDetail.keyId,
                        },
                    ];
                    return ChargeAgent.getAirPharmacyCalculate(params)
                        .catch((error) => new ABCError(error))
                        .toObservable();
                })
            )
            .subscribe((rsp) => {
                if (!(rsp instanceof ABCError) && !!rsp) {
                    this.innerState.airPharmacyTotalPrice = rsp[0]?.goodsTotalPrice || 0;
                    this.update();
                }
            });
    }

    /**
     * 初始化药房信息
     * @private
     */
    private _initPharmacyInfo(): void {
        //多药房---药房名称应该先根据当前初始中药类型（14--中药饮片，15--中药颗粒）来获取药房信息
        const {
            pharmacyInfoConfig,
            selectPharmacyInfo,
            chineseMedicineSpecType,
            isChinesePrescription,
            isWesternPrescription,
            isInfusionPrescription,
        } = this.innerState;
        if (pharmacyInfoConfig?.isOpenMultiplePharmacy) {
            if (!selectPharmacyInfo) {
                let initGoodsTypeId = undefined;
                if (isChinesePrescription) {
                    initGoodsTypeId =
                        chineseMedicineSpecType == ChineseMedicineSpecType.chinesePiece
                            ? GoodsTypeId.medicineChinesePiece
                            : GoodsTypeId.medicineChineseGranule;
                } else if (isWesternPrescription) {
                    initGoodsTypeId = GoodsTypeId.medicineWest;
                } else if (isInfusionPrescription) {
                    initGoodsTypeId = GoodsTypeId.medicineWest;
                }
                const { pharmacyInfo } = (this.innerState.formDetail as PrescriptionChineseForm) ?? {};
                const filterExistPharmacy = pharmacyInfoConfig.filterNormalPharmacyList?.filter(
                    (pharmacy) => pharmacy.no == pharmacyInfo?.no || pharmacy.name == pharmacyInfo?.name
                );
                const pharmacyList = !!filterExistPharmacy?.length
                    ? filterExistPharmacy
                    : pharmacyInfoConfig?.filterPharmacyListWithGoodsType(initGoodsTypeId);
                // 此处过滤药房号这一步的原因：获取默认配置药房信息，如果没找到也会返回一个空的药房信息过来，需要过滤，防止当前没有匹配
                this.innerState.selectPharmacyInfo = !!pharmacyList?.filter((t) => !isNil(t?.no))?.length
                    ? pharmacyList[0]
                    : pharmacyInfoConfig?.filterNormalPharmacyList?.find((k) => k.no == PharmacyType.normal);
            }
        }
    }
    private async _initPageData(): Promise<void> {
        const { isChinesePrescription, isInfusionPrescription, isWesternPrescription, westernMedicineConfig } = this.innerState;
        this._searchClinicId = this.innerState.outpatientSheetDetail?.consultationSheet?.clinicId ?? userCenter.clinic?.clinicId ?? "";

        if (!this.innerState.formDetail) {
            if (isWesternPrescription) {
                this.innerState.formDetail = new PrescriptionWesternForm();
            }
            if (isInfusionPrescription) {
                this.innerState.formDetail = new PrescriptionInfusionForm();
            }
            if (isChinesePrescription) {
                this.innerState.formDetail = new PrescriptionChineseForm();
            }
            this.innerState.formDetail.keyId = UUIDGen.generate();
            this.innerState.formDetail.prescriptionFormItems = [];
        }
        // 清理议价信息
        this.innerState.formDetail.prescriptionFormItems?.forEach((formItem) => {
            formItem.totalPrice = undefined;
            formItem.expectedTotalPriceRatio = undefined;
            formItem.expectedTotalPrice = undefined;
            formItem.expectedUnitPrice = undefined;
            formItem.isTotalPriceChanged = 0;
            formItem.isUnitPriceChanged = 0;
            formItem.unitAdjustmentFeeLastModifiedBy = userCenter.employee?.id ?? userCenter.employee?.employeeId;
        });

        //初始化药品药房、分组问题
        if (isInfusionPrescription) {
            let usage = JsonMapper.deserialize(InfusionPrescriptionUsage, westernMedicineConfig?.prescriptionInfusionDefaultValues);
            const prescriptionGroup = this.innerState.getPrescriptionItemListChunkGroupId();
            prescriptionGroup.forEach((prescriptionList, groupId) => {
                if (!!prescriptionList[0]) {
                    usage = InfusionPrescriptionUsage.fromPrescriptionFormItem(prescriptionList[0]);
                }
                this.innerState.infusionFormUsageGroup.set(groupId, usage);
            });
            if (!prescriptionGroup.size) {
                this.innerState.infusionFormUsageGroup.set(1, usage);
            }
        } else if (isChinesePrescription) {
            const { chineseMedicineConfig } = this.innerState;
            const formDetail = this.innerState.formDetail as PrescriptionChineseForm;
            // 从已有处方点击进入，不需要再次初始化
            this.innerState.searchChineseSpecType = this.innerState.chineseMedicineSpecType;
            if (formDetail.prescriptionFormItems?.length) return;
            //上一次空中药房使用情况
            const _lastAirPharmacyInfo = clinicSharedPreferences.getObject(
                SharedReferenceConstants.LAST_SELECT_VENDOR_INFO
            ) as AirPharmacyDetail;
            //上一次完诊的中药处方用法
            let _lastChinesePrescriptionUsage = !!clinicSharedPreferences.getObject(CHINESE_PRESCRIPTION_USAGE_DEFAULT)
                ? JSON.parse(clinicSharedPreferences.getObject(CHINESE_PRESCRIPTION_USAGE_DEFAULT))
                : undefined;
            _lastChinesePrescriptionUsage = !!_lastChinesePrescriptionUsage
                ? JsonMapper.deserialize(PrescriptionDefaultUsage, {
                      dailyDosage: _lastChinesePrescriptionUsage?.dailyDosage?.name,
                      usage: _lastChinesePrescriptionUsage?.usage?.name,
                      freq: _lastChinesePrescriptionUsage?.freq?.name,
                      usageLevel: _lastChinesePrescriptionUsage?.usageLevel?.name,
                      usageDays: _lastChinesePrescriptionUsage?.usageDays?.name,
                  })
                : undefined;
            const isAirPharmacy = _lastAirPharmacyInfo?.usageScopeId && _lastAirPharmacyInfo.pharmacyType == PharmacyType.air;
            const chineseUsageInfo =
                !!_lastChinesePrescriptionUsage && isAirPharmacy
                    ? _lastChinesePrescriptionUsage
                    : _lastAirPharmacyInfo?.usageScopeId
                    ? await ChineseMedicineConfigProvider.getChinesePRWithScopeId(_lastAirPharmacyInfo.usageScopeId)
                    : ChineseMedicineConfigProvider.getChinesePRWithSpecification(ChineseMedicineSpecType.chinesePiece);
            //空中药房的用法配置--从接口读取
            let airUsage, airFreq, airUsageLevel, airDosage;
            const usageOptions =
                formDetail.vendorInfo?.businessScopeConfig?.usageOptions ?? _lastAirPharmacyInfo?.vendor?.businessScopeConfig?.usageOptions;
            const freqOptions =
                formDetail.vendorInfo?.businessScopeConfig?.freqOptions ?? _lastAirPharmacyInfo?.vendor?.businessScopeConfig?.freqOptions;
            const usageLevelOptions =
                formDetail.vendorInfo?.businessScopeConfig?.usageLevelOptions ??
                _lastAirPharmacyInfo?.vendor?.businessScopeConfig?.usageLevelOptions;
            const dosageOptions =
                formDetail.vendorInfo?.businessScopeConfig?.dosageOptions ??
                _lastAirPharmacyInfo?.vendor?.businessScopeConfig?.dosageOptions;
            //空中药房用法用量--如果上次用法用量存在当前供应商列表中，则取上次用法用量，否则取默认值
            if (isAirPharmacy) {
                //用法
                airUsage = !!usageOptions?.find((t) => t?.name == chineseUsageInfo?.usage)
                    ? usageOptions?.find((t) => t?.name == chineseUsageInfo?.usage)
                    : usageOptions?.find((t) => t?.defaultValue);
                //频率
                airFreq = !!freqOptions?.find((t) => t?.name == chineseUsageInfo?.freq)
                    ? freqOptions?.find((t) => t?.name == chineseUsageInfo?.freq)
                    : freqOptions?.find((t) => t?.defaultValue);
                //用量
                airUsageLevel = !!usageLevelOptions?.find((t) => t?.name == chineseUsageInfo?.usageLevel)
                    ? usageLevelOptions?.find((t) => t?.name == chineseUsageInfo?.usageLevel)
                    : usageLevelOptions?.find((t) => t?.defaultValue);
                //剂量
                airDosage = !!dosageOptions?.find((t) => t?.name == chineseUsageInfo?.dailyDosage)
                    ? dosageOptions?.find((t) => t?.name == chineseUsageInfo?.dailyDosage)
                    : dosageOptions?.find((t) => t?.defaultValue);
                //空中药房颗粒剂配置
                Object.assign(formDetail, {
                    processBagUnit: _lastAirPharmacyInfo.vendor?.processBagUnit,
                    processBagUnitCount: _lastAirPharmacyInfo.vendor?.processBagUnitCount,
                    doseCountLimit: _lastAirPharmacyInfo.vendor?.doseCountLimit,
                });
            }
            Object.assign(formDetail, {
                doseCount: formDetail.doseCount,
                dailyDosage: (
                    (isAirPharmacy
                        ? airDosage
                        : chineseMedicineConfig.dailyDosage!.find((item) => item.name == chineseUsageInfo?.dailyDosage)) ??
                    (isAirPharmacy && !airDosage ? "" : chineseUsageInfo?.dailyDosage)
                ).name,
                usage: (
                    (isAirPharmacy ? airUsage : chineseMedicineConfig.usages!.find((item) => item.name == chineseUsageInfo?.usage)) ??
                    (isAirPharmacy && !airUsage ? "" : chineseUsageInfo?.usage)
                ).name,
                freq: (
                    (isAirPharmacy ? airFreq : chineseMedicineConfig.freq!.find((item) => item.name == chineseUsageInfo?.freq)) ??
                    (isAirPharmacy && !airFreq ? "" : chineseUsageInfo?.freq)
                ).name,
                usageLevel: (
                    (isAirPharmacy
                        ? airUsageLevel
                        : chineseMedicineConfig.usageLevel!.find((item) => item.name == chineseUsageInfo?.usageLevel)) ??
                    (isAirPharmacy && !airUsageLevel ? "" : chineseUsageInfo?.usageLevel)
                ).name,
                usageDays: chineseUsageInfo?.usageDays,
                specification: (
                    this.innerState.pharmacyInfoConfig?.isOpenMultiplePharmacy && _lastAirPharmacyInfo?.pharmacyType == PharmacyType.normal
                        ? _lastAirPharmacyInfo?.localPharmacyType
                        : _lastAirPharmacyInfo?.usageScopeId == "7"
                )
                    ? "中药颗粒"
                    : "中药饮片" ?? "中药饮片",
            });
            //空中药房,需要匹配上次记忆用法用量是否存在当前供应商配置中，不存在，则需要置空（如果对应用法用量不存在，则不进行下面操作，避免复制历史处方后，用法用量不回填）
            if (formDetail.pharmacyType == PharmacyType.air && formDetail.usageScopeId) {
                if (!!usageOptions?.length && !usageOptions?.find((t) => t?.name == formDetail.usage)) {
                    formDetail.usage = "";
                }
                if (!!freqOptions?.length && !freqOptions?.find((t) => t?.name == formDetail.freq)) {
                    formDetail.freq = "";
                }
                if (!!dosageOptions?.length && !dosageOptions?.find((t) => t?.name == formDetail.dailyDosage)) {
                    formDetail.dailyDosage = "";
                }
                if (!!usageLevelOptions?.length && !usageLevelOptions?.find((t) => t?.name == formDetail.usageLevel)) {
                    formDetail.usageLevel = "";
                }
            }
        }
        if (isChinesePrescription || this.innerState.isOpenCoClinic) {
            const formDetail = this.innerState.formDetail as PrescriptionChineseForm;
            if (!formDetail.pharmacyType && isNil(formDetail.pharmacyNo)) this._initPharmacyInfo();
            this._initHistoryAirPharmacy();
        }

        [...this.innerState.getPrescriptionItemListChunkGroupId().keys()].some((id) => id != ChargeUtils.maxGroupId) &&
            (this.innerState.isEditGroup = true);
    }

    /**
     * 计算空中药房服用天数
     * @private
     */
    private computedAirPharmacyUSagDays(): void {
        const formDetail = this.innerState.formDetail as PrescriptionChineseForm;
        const { pharmacyType, medicineStateScopeId, usageScopeId } = formDetail;
        // 空中药房不同的 制法会对应不同的服用量
        const UsageInfoMap = ChineseMedicineUsageInfo.GetChineseUsageParam;
        const value = UsageInfoMap(medicineStateScopeId) || UsageInfoMap(usageScopeId);
        if (pharmacyType == PharmacyType.air && value) {
            this._modifyChinesePrescriptionUsage(formDetail);
            formDetail.usageDays = formDetail.usageDays ?? "";
        }
    }

    private _modifyChinesePrescriptionUsage(prescription: PrescriptionChineseForm): void {
        const usageInfo: UsageInfo = {
            freq: prescription.freq,
            usageLevel: prescription.usageLevel,
        };
        prescription.usageDays = ChargeUtils.calcUsageDays({
            totalDoseWeight: prescription.computePrescriptionDosageWeight() * (prescription.doseCount ?? 1),
            finishedRate: prescription.vendor?.finishedRate,
            isAirPharmacy: prescription.isAirPharmacy,
            usageScopeId: prescription.usageScopeId,
            medicineStateScopeId: prescription.medicineStateScopeId,
            usageInfo: usageInfo,
        });
    }

    private _calculatePrice(): Promise<OutpatientInvoiceDetail> {
        this.innerState.calculating = true;
        this.update();
        if (_.isNumber(this.innerState.outpatientSheetDetail!.expectedTotalPrice)) {
            this.innerState.outpatientSheetDetail!.adjustmentFee = undefined;
        }
        //给相应处方赋值
        this.innerState.outpatientSheetDetail!.medicalRecord = undefined;
        const { isInfusionPrescription, isChinesePrescription, isWesternPrescription, formDetail } = this.innerState;
        //计算处方拆零情况
        formDetail.prescriptionFormItems?.forEach((formItem) => {
            formItem.goodsId = formItem.goodsId ?? formItem.productInfo?.id ?? formItem.id;
            formItem.useDismounting = formItem.unit ? (formItem.goodsInfo.useDismounting(formItem.unit) ? 1 : 0) : undefined;
            //计算price
            !!formItem.unit && (formItem.unitPrice = formItem.goodsInfo.unitPriceWithUnit(formItem.unit));
        });
        if (isWesternPrescription) {
            this.innerState.outpatientSheetDetail!.prescriptionWesternForms = [formDetail];
        } else if (isChinesePrescription) {
            const cloneFormDetail = _.cloneDeep(formDetail);
            const { eqConversionRule, pharmacyType } = cloneFormDetail;
            const isChinesePiece = eqConversionRule === ChineseMedicineSpecType.chinesePiece;
            const isLocalPharmacy = pharmacyType === PharmacyType.normal;
            cloneFormDetail.prescriptionFormItems?.forEach((formItem) => {
                const { goodsInfo } = formItem;
                const { typeId, pieceUnit } = goodsInfo;
                const isMedicineChineseGranule = GoodsTypeId.medicineChineseGranule === typeId;
                if (!(userCenter.enableEqCoefficient && isLocalPharmacy && isChinesePiece && isMedicineChineseGranule && (pieceUnit === "g" || pieceUnit === "克"))) {
                   delete formItem.eqCoefficient
                }
            })
            this.innerState.outpatientSheetDetail!.prescriptionChineseForms = [cloneFormDetail as PrescriptionChineseForm];
        } else if (isInfusionPrescription) {
            this.innerState.outpatientSheetDetail!.prescriptionInfusionForms = [formDetail];
        }
        return OutpatientAgent.chargeCalculate(this.innerState.outpatientSheetDetail!);
    }

    private combinedAirPharmacyPrescription(airPharmacyList?: PrescriptionChineseForm[]): AirPharmacyCalculateForm[] | undefined {
        if (!airPharmacyList?.length) {
            return undefined;
        }

        return airPharmacyList.map((item) => {
            const isPiece = ChineseMedicineSpecType.chinesePiece === ChineseMedicineSpecType.typeFromName(item.specification ?? "");
            const { deliveryInfo, keyId, usageScopeId, vendorId } = item;
            const { dailyDosage, freq, usage, usageLevel } = item;
            const goodsTypeId = isPiece ? ChineseGoodType.chinesePiece : ChineseGoodType.chineseGranule;
            const items = item.prescriptionFormItems?.map((sub) => {
                const { displayName: name, productId, unit, unitCount, unitPrice } = sub;
                const doseCount = item.doseCount;
                return { name, productId, unit, unitCount, unitPrice, doseCount };
            });
            const sort = item.sort;

            return {
                deliveryInfo,
                keyId,
                usageScopeId,
                vendorId,
                usageInfo: { dailyDosage, freq, usage, usageLevel },
                goodsTypeId,
                items,
                sort,
            };
        });
    }

    private _showMinimumLimitTips(): boolean {
        const formDetail = this.innerState.formDetail as PrescriptionChineseForm;
        const { chineseMedicineSpecType, currentAirPharmacyKeLiConfig } = this.innerState;
        const { doseCount = 1, medicineStateScopeId, vendor } = formDetail;
        const minimum = vendor?.minimum;
        const pharmacyType = vendor?.pharmacyType ?? formDetail.pharmacyType;
        //颗粒剂开方剂数是否限制
        const isDoseCountLimit = currentAirPharmacyKeLiConfig?.doseCountLimit == 1;
        // 空中药房才做以下验证
        // 没有选中药品的时候不做验证
        if (pharmacyType != PharmacyType.air || !formDetail.prescriptionFormItems?.length) {
            return true;
        }

        // 中药饮片 + 代煎 剂量至少为 3
        if (chineseMedicineSpecType === ChineseMedicineSpecType.chinesePiece && medicineStateScopeId === MedicineScopeId.daiJian) {
            if (doseCount < 3) {
                showConfirmDialog("", `3剂起煎`).then();
                return false;
            }
        }
        //颗粒剂开方剂数限制
        if (
            isDoseCountLimit &&
            chineseMedicineSpecType === ChineseMedicineSpecType.chineseGranule &&
            medicineStateScopeId === MedicineScopeId.keLi
        ) {
            if (doseCount % 2 !== 0) {
                showConfirmDialog("", `${formDetail.vendorName ?? formDetail.vendor?.pharmacyName}药需剂数为双数才可调剂`).then();
                return false;
            }
        }
        let dosageValue = 0;
        formDetail.prescriptionFormItems?.forEach((item) => {
            if (item.unit == "g") dosageValue += item.unitCount ?? 0;
        });
        dosageValue = dosageValue * (doseCount ?? 0);
        if (_.isUndefined(minimum)) return true;

        if (dosageValue < minimum) {
            showConfirmDialog("", `起做量${minimum}g`).then();
            return false;
        }
        // 空中药房-代煎-服用量必填
        if (medicineStateScopeId == MedicineScopeId.daiJian) {
            if (!formDetail.usageLevel) {
                showConfirmDialog("", `服用量不能为空`).then();
                return false;
            }
            if (!formDetail.dailyDosage) {
                showConfirmDialog("", `每日剂量不能为空`).then();
                return false;
            }
            if (!formDetail.freq) {
                showConfirmDialog("", `频率不能为空`).then();
                return false;
            }
            // 加工袋数不能为空
            if (!formDetail.processBagUnitCount || !formDetail.totalProcessCount) {
                showConfirmDialog("", `加工袋数不能为空`).then();
                return false;
            }
        }

        return true;
    }

    @actionEvent(_EventInit)
    private async *_mapEventInit(/*event: _EventInit*/): AsyncGenerator<State> {
        await this._initPageConfig();
        this._initPageTrigger();
        await this._initPageData();
        this.innerState.originFormDetail = _.cloneDeep(this.innerState.formDetail);
        this.update();
    }

    @actionEvent(_EventUpdate)
    private async *_mapEventUpdate(event: _EventUpdate): AsyncGenerator<State> {
        if (event.state) {
            yield event.state;
            return;
        }
        yield this.innerState.clone();
    }

    private _doUpdateCalculatePrice(event: _EventUpdateCalculatePrice): void {
        this.innerState.calculating = event.calculating;
        this.innerState.calculateFailed = event.error;
        this.update();

        if (event.calculateRspData == null) return;

        this.innerState.outpatientSheetDetail!.totalPrice = event.calculateRspData.totalPrice;
        this.innerState.outpatientSheetDetail!.adjustmentFee = event.calculateRspData.adjustmentFee;
        this.innerState.outpatientSheetDetail!.sourceTotalPrice = event.calculateRspData.sourceTotalPrice;
        this.innerState.outpatientSheetDetail!.isTotalPriceChanged = event.calculateRspData.isTotalPriceChanged;
        this.innerState.outpatientSheetDetail!.registrationFee = event.calculateRspData.registrationFee; //挂号费也进行摊费
        this.innerState.outpatientSheetDetail!.chargeRoundingTips = _.isNil(event.calculateRspData.chargeRoundingTips)
            ? undefined
            : event.calculateRspData.chargeRoundingTips;
        OutpatientUtils.copyPriceAttrToPrescription(
            event.calculateRspData.productForms!,
            this.innerState.outpatientSheetDetail?.productForms ?? [],
            event.shouldNotCopyExpectedPrice
        );
        const { isInfusionPrescription, isChinesePrescription, isWesternPrescription } = this.innerState;
        if (isWesternPrescription) {
            OutpatientUtils.copyPriceAttrToPrescription(
                event.calculateRspData.prescriptionWesternForms!,
                this.innerState.outpatientSheetDetail?.prescriptionWesternForms ?? [],
                event.shouldNotCopyExpectedPrice
            );
        } else if (isChinesePrescription) {
            OutpatientUtils.copyPriceAttrToPrescription(
                event.calculateRspData.prescriptionChineseForms!,
                this.innerState.outpatientSheetDetail?.prescriptionChineseForms ?? [],
                event.shouldNotCopyExpectedPrice
            );
        } else if (isInfusionPrescription) {
            OutpatientUtils.copyPriceAttrToPrescription(
                event.calculateRspData.prescriptionInfusionForms!,
                this.innerState.outpatientSheetDetail?.prescriptionInfusionForms ?? [],
                event.shouldNotCopyExpectedPrice
            );
        }
        OutpatientUtils.copyPriceAttrToPrescription(
            event.calculateRspData.prescriptionExternalForms!,
            this.innerState.outpatientSheetDetail?.prescriptionExternalForms ?? [],
            event.shouldNotCopyExpectedPrice
        );
    }

    private async _initMedicineUsage(formItem: PrescriptionFormItem): Promise<void> {
        const { goodsInfo } = formItem;
        const sellUnits = goodsInfo.sellUnits;
        Object.assign(formItem, {
            unit: ABCUtils.first(sellUnits) ?? undefined,
            dosageUnit: ABCUtils.first(this.innerState.westernMedicineConfig?.dosageUnitStringList ?? []),
            totalPrice: MedicineAddPageUtils.computeMedicineSingleTotalPrice(formItem),
            totalPriceRatio: 1,
        });
        const units = goodsInfo.usageUnits;
        if (!!units.length) formItem.dosageUnit = units[0];
        if (this.innerState.isWesternPrescription || this.innerState.isInfusionPrescription) {
            formItem.unit = formItem.unit ?? WesternMedicine.unit[0].name;
            formItem.unitPrice = goodsInfo.unitPriceWithUnit(formItem.unit);
        }
        //更新药品库存
        const currentSelStock = goodsInfo?.pharmacyGoodsStockList?.find((item) => item?.pharmacyNo == (formItem.pharmacyNo ?? 0));
        if (!!currentSelStock) {
            const { stockPackageCount = 0, stockPieceCount = 0 } = currentSelStock || {};
            goodsInfo.stockPackageCount = stockPackageCount;
            goodsInfo.stockPieceCount = stockPieceCount;
        }

        if (this.innerState.isInfusionPrescription || this.innerState.isWesternPrescription) {
            let age;
            const { patient } = this.innerState.outpatientSheetDetail ?? {};
            if (!!patient) {
                age = (patient.age?.year ?? 0) + (patient.age?.month ?? 0) / 10;
            }
            const originUsage = await CDSSAgent.getMedicineUsage({
                medicineCadn: goodsInfo.medicineCadn,
                goodsId: goodsInfo.id,
                type: goodsInfo.type,
                age: age,
                sex: patient?.sex,
            }).catchIgnore();
            if (!!originUsage) {
                const { westernMedicineConfig } = this.innerState;
                const validDosageUnit =
                    _.find(goodsInfo.usageUnits, (item) => item == originUsage.dosageUnit) ||
                    originUsage.dosageUnit == OutpatientConst.dosageUsageProperty;
                const validUnit = _.find(goodsInfo.sellUnits, goodsInfo.unit);

                formItem.usage = originUsage.usage;
                formItem.freq = westernMedicineConfig?.freq?.find((_freq) => _freq.en == originUsage.freq)?.en ?? formItem.freq;
                formItem.dosage = Number(originUsage.dosage) ? Number(originUsage.dosage) : undefined;
                formItem.dosageUnit = ABCUtils.first(goodsInfo.usageUnits) ?? originUsage.dosageUnit ?? "";

                if (validDosageUnit) {
                    formItem.dosageUnit = originUsage.dosageUnit;
                }
                if (validUnit) {
                    formItem.unit = originUsage?.unit;
                } else {
                    formItem.unit = ABCUtils.first(sellUnits) ?? originUsage.unit ?? "";
                }

                //完善当前选中的药房信息
                const pharmacyInfo = this.innerState.pharmacyInfoConfig?.filterNormalPharmacyList?.find(
                    (pharmacy) => pharmacy.no == goodsInfo?.pharmacyNo
                );
                if (!!pharmacyInfo) {
                    formItem.pharmacyNo = pharmacyInfo.no;
                    formItem.pharmacyType = pharmacyInfo.type;
                    formItem.pharmacyName = pharmacyInfo.name ?? formItem.pharmacyName;
                    // medicineUsage.pharmacyInfo = {
                    //     name: pharmacyInfo.name,
                    //     no: pharmacyInfo.no,
                    //     type: pharmacyInfo.type,
                    //     typeName: pharmacyInfo.typeName,
                    //     pharmacyTag: pharmacyInfo.extendInfo?.tag,
                    // };
                }
            }
        } else {
            this.innerState.pendingActiveFocusItem = new Completer();
        }
        //注射类使用处方组中的用法用量
        if (this.innerState.isInfusionPrescription) {
            const infusionPrescriptionUsage = this.innerState.infusionFormUsageGroup.get(formItem.groupId ?? 0);
            formItem.usage = infusionPrescriptionUsage?.usage ?? formItem.usage;
            formItem.freq = infusionPrescriptionUsage?.freq ?? formItem.freq;
            formItem.ivgtt = infusionPrescriptionUsage?.ivgtt;
            formItem.days = infusionPrescriptionUsage?.days;
            formItem.ivgttUnit = infusionPrescriptionUsage?.ivgttUnit;
        }
        if (this.innerState.isChinesePrescription) {
            formItem.unitPrice = goodsInfo.unitPriceWithUnit("g");
            this.innerState.searchChineseSpecType = this.innerState.chineseMedicineSpecType;
            //第一次添加空中药房触发一次拉取空中药房信息
            const pharmacyType = this.innerState.formDetail?.pharmacyType;
            if (pharmacyType == PharmacyType.air && this.innerState.formDetail.prescriptionFormItems?.length === 1) {
                this._loadVendorList.next();
            }
        }
    }

    private async _trans(event: _EventChineseMedicineSpecType): Promise<boolean> {
        /**
         * 更改类型后判断药品是否转换类型
         */
        const toSpec = ChineseMedicineSpecType.fullNames()[event.type];
        const toSpecShort = ChineseMedicineSpecType.displayNames()[event.type];
        const isOpenMultiplePharmacy = this.innerState.pharmacyInfoConfig?.isOpenMultiplePharmacy;

        const transformList = this.innerState.formDetail.prescriptionFormItems ?? [];

        let queryInfo = DialogIndex.positive;
        if (transformList.length) {
            if (!event.forceTrans) {
                //多药房
                queryInfo = await showQueryDialog(
                    `处方药态将转换为${toSpecShort}`,
                    <View>
                        <Text style={TextStyles.t16NT2.copyWith({ color: Colors.t2 })}>
                            {`是否将处方中的所有药品转化为${toSpecShort}？`}
                        </Text>
                    </View>,
                    `确认转化`,
                    `取消`
                );
            }
            if (queryInfo == undefined) {
                return false;
            } else if (queryInfo == DialogIndex.positive) {
                if (userCenter.enableEqCoefficient) {
                    if (event.type !== ChineseMedicineSpecType.chineseGranule) {
                        this.innerState.formDetail.eqConversionRule = undefined;
                    } else {
                        this.innerState.formDetail.eqConversionRule = ChineseMedicineSpecType.chinesePiece;
                    }
                }
                transformList.forEach((item) => {
                    item.keyId = item.keyId ?? UUIDGen.generate();
                    if (item.cMSpec == toSpec) return;
                    item.__cMSpec = item.cMSpec;
                    item.cMSpec = toSpec;
                });
                //只有当转换类型cMSpec与内部cMSpec不一致时，才走trim匹配规则
                await GoodsAgent.searchChineseMedicineGoodsBySpec({
                    cMSpec: toSpec,
                    list: transformList?.map((item) => ({
                        medicineCadn: item.displayName,
                        goodsId: item.goodsId ?? item.productId ?? item.productInfo?.id ?? "",
                        manufacturer: item.manufacturer,
                        keyId: item.keyId ?? UUIDGen.generate(),
                        cMSpec: item.__cMSpec ?? ChineseMedicineSpecType.fullNames()[this.innerState.chineseMedicineSpecType!],
                        pieceUnit: item.pieceUnit ?? "",
                        extendSpec: item.goodsInfo?.extendSpec ?? "",
                    })),
                    pharmacyNo:
                        isOpenMultiplePharmacy && event?.pharmacyType == PharmacyType.normal
                            ? this.innerState.selectPharmacyInfo?.no
                            : this.innerState.formDetail?.pharmacyNo,
                }).then((rsp) => {
                    const { eqConversionRule } = this.innerState.formDetail;
                    this.innerState.formDetail.prescriptionFormItems = this.innerState.formDetail.prescriptionFormItems?.map((item) => {
                        const isExistGoods = rsp?.find((t) => t.keyId == item.keyId || t.keyId == item.goodsInfo.keyId);
                        const _newGoodInfo = !!isExistGoods?.goods?.id ? isExistGoods?.goods : undefined;
                        if (!!_newGoodInfo) {
                            const tempItem = OutpatientUtils.fillPrescriptionFromItem(
                                item,
                                _newGoodInfo,
                                JsonMapper.deserialize(MedicineUsage, {
                                    unit: _newGoodInfo?.unit,
                                    unitCount: item?.unitCount ?? 1,
                                })
                            );
                            const { typeId, pieceUnit } = _newGoodInfo;
                            const isMedicineChineseGranule = GoodsTypeId.medicineChineseGranule === typeId;
                            let obj = {};
                            // 中药颗粒 & 单位为g/克
                            if (
                                userCenter.enableEqCoefficient &&
                                isMedicineChineseGranule &&
                                (pieceUnit === "g" || pieceUnit === "克") &&
                                eqConversionRule === ChineseMedicineSpecType.chinesePiece
                            ) {
                                obj = {
                                    eqCoefficient: _newGoodInfo?.eqCoefficient,
                                    eqUnitCount: item?.eqUnitCount || item?.unitCount,
                                    unitCount: undefined,
                                };
                            } else {
                                obj = {
                                    eqCoefficient: undefined,
                                    eqUnitCount: undefined,
                                    unitCount: item?.eqUnitCount || item?.unitCount,
                                };
                            }
                            Object.assign(tempItem, obj);
                            return tempItem;
                        } else {
                            item.unitCount = item.eqUnitCount || item.unitCount;
                            item.eqCoefficient = undefined;
                            item.eqUnitCount = undefined;
                        }
                        item.productInfo = _newGoodInfo;
                        return item;
                    });
                    this.update();
                });
            }
        }
        //中药饮片颗粒不可混开情况下，点击"取消",不应该转换类型
        // 应该是点击取消，都不应该进行转换，和中药饮片颗粒不可混开开关无关
        const isNotNeedChangeType = queryInfo == DialogIndex.negative;
        if (!isNotNeedChangeType) {
            this.innerState.formDetail.specification = ChineseMedicineSpecType.fullNames()[event.type];
        }
        return !isNotNeedChangeType;
    }
}
