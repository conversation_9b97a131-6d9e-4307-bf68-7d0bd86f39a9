import React from "react";
import { View } from "@hippy/react";
import { sharedPreferences } from "./sub-project/base-business/preferences/shared-preferences";
import { AppInfo } from "./sub-project/base-business/config/app-info";
import { LogUtils } from "./sub-project/common-base-module/log";
import { LogLevel } from "./sub-project/common-base-module/log/log-utils";
import { ThemeManager } from "./sub-project/theme/themes";
import { BaseComponent, setComponentErrorHandler } from "./sub-project/base-ui/base-component";
import { bugly } from "./sub-project/base-business/bugly/bugly";
import { RuntimeConstantsManager } from "./sub-project/data/constants/runtime-constants-manager";
import abcI18Next from "./sub-project/language/config";
import { AIMedicalRecordsManager } from "./sub-project/AI";
import eventBus, { _initEventBus } from "./event-bus";
import { AbcCustomKeyboardView, AbcInputAccessoryView, AbcTextInput } from "./sub-project/base-ui/views/abc-text-input";
import { DeviceUtils } from "./sub-project/base-ui/utils/device-utils";
import { Colors } from "./sub-project/theme";

interface AppProps {
    userInfo?: string;
    isSimulator?: boolean;
    __instanceId__?: string;
    action: string;
    logLevel: number;
    theme: number;
    appVersion: string;
    osVersion: string;
    pwd: string; //插件的根目录
    launchGrayFlag?: number; //启动时运行js bundle时灰度标记

    //Android是否打开了全屏模式
    androidFullScreen?: boolean;
}

export default class App extends BaseComponent<AppProps> {
    //懒加载相对模板，用于控制启动顺序，防止和view在主题设置好之前初始化了
    static AppTextInputAccessoryGlobal: AbcTextInput; //全局的调用的自定义键盘
    static AppTextInputCustomKeyboardGlobal: AbcTextInput; //全局的调用的自定义键盘
    static ABCNavigator: any; // ABCNavigator
    static ABCApiNetwork: any; //ABCApiNetwork
    static URLProtocols: any; // URLProtocols
    static userCenter: any; //userCenter
    static environment: any; //environment
    static URLRouter: any; //URLRouter

    _initCompleted = false;

    _hasNotifyAppShow = false; //是否已经通知app 显示事件了

    constructor(props: AppProps) {
        super(props);
        LogUtils.d("App.start param = " + JSON.stringify(this.props));
        this._preInitSync();
        this._initAsync();
    }

    render(): JSX.Element {
        const { action } = this.props;
        LogUtils.d("App.start params = " + action);
        if (!this._initCompleted) return <View />;
        //延时加载url-router,防止里面引用的页面,在ThemeManager.setTheme之前创建了style,导致主题设置失效

        let initUrl = action;
        if (!App.userCenter.isLogin()) {
            initUrl = App.URLProtocols.ABCURL_LOGIN + "?jumpUrl=" + action;
        } else if (!App.userCenter.clinic) {
            initUrl = App.URLProtocols.ABCURL_CLINIC_CHANGE + "?jumpUrl=" + action;
        }

        if (App.URLRouter) {
            //注册路由组件文件
            require("./sub-project/url-dispatcher/router-component");
        }

        return (
            <View
                style={{ flex: 1 }}
                onLayout={
                    this._hasNotifyAppShow
                        ? undefined
                        : (e) => {
                              // @ts-ignore
                              const { width, height } = e.layout;
                              if (width && height) {
                                  const { bootLoader } = require("./sub-project/boot/boot-loader");
                                  bootLoader.onAppShow();
                                  this._hasNotifyAppShow = true;
                              }
                          }
                }
            >
                <App.ABCNavigator
                    ref={(ref: any) => {
                        App.ABCNavigator.navigator = ref;
                        if (ref) {
                            const { bootLoader } = require("./sub-project/boot/boot-loader");
                            bootLoader.onNavigatorReady();
                        }
                    }}
                    initialRoute={{
                        routeName: App.URLProtocols.ABCURL_HOME,
                        component: App.URLRouter.actionToPage(initUrl),
                        animated: false,
                    }}
                    router={(url: string) => App.URLRouter.actionToPage(url)}
                    style={{ flex: 1 }}
                ></App.ABCNavigator>

                {/* 添加全局 自定义浮窗和自定义键盘 */}
                {App.AppTextInputAccessoryGlobal ? (
                    <View style={{ overflow: "hidden", backgroundColor: Colors.white }}>
                        <AbcInputAccessoryView
                            ref={(ref: any) => {
                                eventBus.inputAccessView = ref;
                            }}
                            textInput={App.AppTextInputAccessoryGlobal}
                        />
                    </View>
                ) : null}
                {App.AppTextInputCustomKeyboardGlobal ? (
                    <View style={{ overflow: "hidden", backgroundColor: Colors.white }}>
                        <AbcCustomKeyboardView
                            ref={(ref: any) => {
                                eventBus.customKeyboardHandler = ref;
                            }}
                            textInput={App.AppTextInputCustomKeyboardGlobal}
                        />
                    </View>
                ) : null}
            </View>
        );
    }

    private _preInitSync() {
        const { logLevel, appVersion, osVersion, pwd, launchGrayFlag, androidFullScreen } = this.props;
        AppInfo.init(appVersion, osVersion, pwd, launchGrayFlag, androidFullScreen);
        LogUtils.setLogLevel(logLevel ?? LogLevel.WARNING);
        //监听组件渲染异常
        setComponentErrorHandler((error, errorInfo) => {
            bugly.postCatchedException(`ComponentError`, errorInfo ? errorInfo.toString() : "", error && error.stack ? error.stack : "");
        });
    }

    private async _initAsync() {
        await sharedPreferences.init().catchIgnore();
        const { environment } = require("./sub-project/base-business/config/environment");
        App.environment = environment;
        await environment.init().catchIgnore();

        sharedPreferences.setObject("startProps", this.props);

        ThemeManager.init();

        RuntimeConstantsManager.init();

        const { bootLoader } = require("./sub-project/boot/boot-loader");
        bootLoader.onAppStart();

        const { ABCNavigator } = require("./sub-project/base-ui/views/abc-navigator");
        App.ABCNavigator = ABCNavigator;

        const { ABCApiNetwork } = require("./sub-project/net");
        App.ABCApiNetwork = ABCApiNetwork;

        const { abcNetDelegate } = require("./sub-project/net/abc-net-delegate");
        App.ABCApiNetwork.delegate = abcNetDelegate;

        const { URLProtocols } = require("./sub-project/url-dispatcher");
        App.URLProtocols = URLProtocols;

        const { URLRouter } = require("./sub-project/url-dispatcher");
        App.URLRouter = URLRouter;

        const { userCenter } = require("./sub-project/user-center");
        App.userCenter = userCenter;
        await userCenter.init().catchIgnore();
        const { initializeGoodsInfoCallbacks } = require("./sub-project/base-business/data/goods-info-initializer");
        initializeGoodsInfoCallbacks();
        const {
            chainSharedPreferences,
            clinicSharedPreferences,
            employeeSharedPreferences,
        } = require("./sub-project/base-business/preferences/scoped-shared-preferences");

        //由于clinic 和chain 的preference存储位置依赖于userCenter里保存的用户登录信息
        //所以需要userCenter初始化后才能初始化
        await clinicSharedPreferences.init().catchIgnore();
        await chainSharedPreferences.init().catchIgnore();
        await employeeSharedPreferences.init().catchIgnore();

        this._initCompleted = true;

        const lang = sharedPreferences.getString("abcLanguageType");
        await abcI18Next.init(lang);

        // 只需要在鸿蒙上做监听，其他设备走原始逻辑
        if (DeviceUtils.isOhos()) {
            await _initEventBus(this);
        }

        if (App.userCenter.isLogin()) {
            AIMedicalRecordsManager.runVoiceRecordService();
        }

        this.setState({});
    }
}
